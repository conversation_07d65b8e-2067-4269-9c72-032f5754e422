// Enhanced Family Type Package Calculator following Quote Generator workflow
import { getQuoteClient } from '../../lib/supabaseManager';

// Interfaces matching the Quote Generator and family_type table
interface FamilyTypeDB {
  family_id: string;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;
  no_of_child: number; // Children ≤5 (free)
  no_of_children: number; // Children 6-12 (charged)
  family_count: number;
  cab_type: string;
  cab_capacity: number;
  rooms_need: number;
}

interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  no_of_persons: number;
  children: number;
  infants: number;
  extra_adults: number;
  total_cost: number;
  commission_rate?: number;
  discount_amount?: number;
  gst_rate?: number;
}

interface HotelRow {
  hotelName: string;
  roomType: string;
  price: number;
  mealPlan: string;
  noOfRooms: number;
  stayNights: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  gstType: '0' | '12' | '18' | 'NET' | 'EXC';
  tacPercentage: number;
  tacAmount: number;
  info: string;
  stayPrice: number;
  gstAmount: number;
}

interface QuoteMappingData {
  id: string;
  quote_id: string;
  quote_name: string;
  customer_name: string;
  destination: string;
  hotel_mappings: any[];
  vehicle_mappings: any[];
  additional_costs: {
    meal_cost_per_person: number;
    ferry_cost: number;
    activity_cost_per_person: number;
    guide_cost_per_day: number;
    parking_toll_multiplier: number;
  };
}

interface FamilyPackageCalculation {
  familyType: FamilyTypeDB;
  roomCalculation: {
    roomsNeeded: number;
    extraAdults: number;
    childrenCharged: number;
    childrenFree: number;
    infantsFree: number;
    totalOccupancy: number;
  };
  hotelCosts: {
    baseRoomCost: number;
    extraAdultCost: number;
    childrenCost: number;
    infantCost: number;
    totalHotelCost: number;
    gstAmount: number;
    tacAmount: number;
  };
  additionalCosts: {
    meals: number;
    transportation: number;
    cabSightseeing: number;
    ferry: number;
    activities: number;
    guide: number;
    parkingToll: number;
    totalAdditionalCosts: number;
  };
  vehicleCosts: {
    vehicleType: string;
    baseCost: number;
    multiplier: number;
    totalVehicleCost: number;
  };
  finalCalculation: {
    subtotal: number;
    discount: number;
    afterDiscount: number;
    commission: number;
    afterCommission: number;
    gst: number;
    grandTotal: number;
  };
}

// Phase 1: Fetch family types from TripXplo Quote DB
export const fetchFamilyTypesFromDB = async (): Promise<FamilyTypeDB[]> => {
  try {
    const supabase = getQuoteClient();
    const { data, error } = await supabase
      .from('family_type')
      .select(`
        family_id,
        family_type,
        no_of_adults,
        no_of_infants,
        no_of_child,
        no_of_children,
        family_count,
        cab_type,
        cab_capacity,
        rooms_need
      `)
      .order('family_id');

    if (error) {
      console.error('Error fetching family types:', error);
      return [];
    }

    console.log(`✅ Fetched ${data?.length || 0} family types from database`);
    return data || [];
  } catch (error) {
    console.error('Exception fetching family types:', error);
    return [];
  }
};

// Phase 2: Calculate room requirements following hotel industry standards
export const calculateFamilyRoomRequirements = (familyType: FamilyTypeDB) => {
  // Extract data from family_type table
  const adults = familyType.no_of_adults;
  const childrenUnder5 = familyType.no_of_child; // Free
  const children6to12 = familyType.no_of_children; // Charged
  const infants = familyType.no_of_infants; // Free
  const familyCount = familyType.family_count;
  const roomsFromDB = familyType.rooms_need;
  
  // Hotel Room Calculation Logic:
  // Standard/Deluxe Room: 2 Adults + 2 Children ≤5 + 1 Extra Adult (max 3 adults)
  // Family Room: 4 Adults + 2 Children ≤5 + 1 Extra Adult (max 5 adults)
  
  let roomsNeeded = roomsFromDB; // Use database value as primary
  let extraAdults = 0;
  
  // If database doesn't specify rooms, calculate based on occupancy rules
  if (roomsNeeded === 0) {
    // Standard calculation: 2 adults per room + 1 extra adult allowed
    roomsNeeded = Math.ceil(adults / 3); // Max 3 adults per room
  }
  
  // Calculate extra adults beyond standard room capacity
  const standardCapacity = roomsNeeded * 2; // 2 adults per room standard
  extraAdults = Math.max(0, adults - standardCapacity);
  
  return {
    roomsNeeded,
    extraAdults,
    childrenCharged: children6to12, // Children 6-12 (charged)
    childrenFree: childrenUnder5, // Children ≤5 (free)
    infantsFree: infants, // Infants ≤2 (free)
    totalOccupancy: adults + children6to12 + childrenUnder5 + infants,
    cabType: familyType.cab_type,
    cabCapacity: familyType.cab_capacity
  };
};

// Phase 3: Calculate hotel costs based on Quote Generator logic
export const calculateFamilyHotelCosts = (
  familyType: FamilyTypeDB,
  roomReq: any,
  hotelRows: HotelRow[]
) => {
  let totalHotelCost = 0;
  let totalGstAmount = 0;
  let totalTacAmount = 0;
  let baseRoomCost = 0;
  let extraAdultCost = 0;
  let childrenCost = 0;
  let infantCost = 0;

  hotelRows.forEach(row => {
    // Base room cost
    const roomNightPrice = row.price * roomReq.roomsNeeded * row.stayNights;
    baseRoomCost += roomNightPrice;

    // Extra costs
    const extraAdultTotal = row.extraAdultCost * roomReq.extraAdults * row.stayNights;
    const childrenTotal = row.childrenCost * roomReq.childrenCharged * row.stayNights;
    const infantTotal = row.infantCost * roomReq.infantsFree * row.stayNights;
    
    extraAdultCost += extraAdultTotal;
    childrenCost += childrenTotal;
    infantCost += infantTotal;

    // Subtotal before GST and TAC
    const subtotal = roomNightPrice + extraAdultTotal + childrenTotal + infantTotal;

    // GST calculation
    let gstAmount = 0;
    if (row.gstType !== 'NET' && row.gstType !== 'EXC') {
      gstAmount = (subtotal * Number(row.gstType)) / 100;
    }

    // TAC calculation
    const tacAmount = (subtotal * row.tacPercentage) / 100;

    // Final price calculation
    let finalPrice = subtotal + tacAmount;
    if (row.gstType !== 'EXC') {
      finalPrice += gstAmount;
    }

    totalHotelCost += finalPrice;
    totalGstAmount += gstAmount;
    totalTacAmount += tacAmount;
  });

  return {
    baseRoomCost,
    extraAdultCost,
    childrenCost,
    infantCost,
    totalHotelCost,
    gstAmount: totalGstAmount,
    tacAmount: totalTacAmount
  };
};

// Phase 4: Calculate additional costs based on Quote Mapping
export const calculateFamilyAdditionalCosts = (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData: QuoteMappingData,
  nights: number
) => {
  const familyCount = familyType.family_count;
  const additionalCosts = quoteMappingData.additional_costs;

  // Scale costs based on family size vs baseline
  const baselineTotal = baselineQuote.no_of_persons + baselineQuote.extra_adults + baselineQuote.children + baselineQuote.infants;
  const scalingFactor = familyCount / Math.max(baselineTotal, 1);

  const meals = additionalCosts.meal_cost_per_person * familyCount;
  const ferry = additionalCosts.ferry_cost;
  const activities = additionalCosts.activity_cost_per_person * familyCount;
  const guide = additionalCosts.guide_cost_per_day * nights;
  const parkingToll = baselineQuote.total_cost * 0.05 * additionalCosts.parking_toll_multiplier; // 5% of baseline for parking/toll

  // Transportation costs (scaled from baseline)
  const baseTransportation = baselineQuote.total_cost * 0.25; // Assume 25% is transportation
  const transportation = baseTransportation * scalingFactor;
  const cabSightseeing = baseTransportation * 0.3 * scalingFactor; // 30% of transportation for sightseeing

  return {
    meals,
    transportation,
    cabSightseeing,
    ferry,
    activities,
    guide,
    parkingToll,
    totalAdditionalCosts: meals + transportation + cabSightseeing + ferry + activities + guide + parkingToll
  };
};

// Vehicle cost calculation based on family type
export const calculateFamilyVehicleCosts = (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData: QuoteMappingData
) => {
  const cabType = familyType.cab_type.toLowerCase();
  const familyCount = familyType.family_count;
  
  // Find matching vehicle from mappings
  const vehicleMapping = quoteMappingData.vehicle_mappings.find(vm => 
    vm.vehicle_type.toLowerCase().includes(cabType.split(' ')[0]) &&
    vm.max_capacity >= familyCount
  );

  let vehicleType = familyType.cab_type;
  let multiplier = 1.0;
  let baseCost = baselineQuote.total_cost * 0.25; // Assume 25% is vehicle cost

  if (vehicleMapping) {
    vehicleType = vehicleMapping.vehicle_type;
    if (vehicleMapping.pricing_type === 'actual_cost') {
      baseCost = vehicleMapping.base_cost;
      multiplier = 1.0;
    } else {
      multiplier = vehicleMapping.cost_multiplier;
    }
  } else {
    // Fallback multipliers based on capacity
    if (familyCount <= 4) multiplier = 1.0; // Sedan
    else if (familyCount <= 7) multiplier = 1.2; // Innova
    else if (familyCount <= 12) multiplier = 1.4; // Tempo Traveller
    else multiplier = 1.6; // Mini Bus
  }

  return {
    vehicleType,
    baseCost,
    multiplier,
    totalVehicleCost: baseCost * multiplier
  };
};

// Main calculation function following Quote Generator workflow
export const calculateEnhancedFamilyTypePackage = async (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData: QuoteMappingData,
  hotelRows: HotelRow[]
): Promise<FamilyPackageCalculation> => {
  
  // Phase 2: Calculate room requirements
  const roomCalculation = calculateFamilyRoomRequirements(familyType);
  
  // Phase 3: Calculate hotel costs
  const hotelCosts = calculateFamilyHotelCosts(familyType, roomCalculation, hotelRows);
  
  // Phase 4: Calculate additional costs
  const nights = hotelRows.reduce((sum, row) => Math.max(sum, row.stayNights), 1);
  const additionalCosts = calculateFamilyAdditionalCosts(familyType, baselineQuote, quoteMappingData, nights);
  
  // Calculate vehicle costs
  const vehicleCosts = calculateFamilyVehicleCosts(familyType, baselineQuote, quoteMappingData);
  
  // Phase 5: Final calculations (same as Quote Generator)
  const subtotal = hotelCosts.totalHotelCost + additionalCosts.totalAdditionalCosts + vehicleCosts.totalVehicleCost;
  const discount = baselineQuote.discount_amount || 0;
  const afterDiscount = Math.max(0, subtotal - discount);
  
  const commissionRate = baselineQuote.commission_rate || 5;
  const commission = afterDiscount * (commissionRate / 100);
  const afterCommission = afterDiscount + commission;
  
  const gstRate = baselineQuote.gst_rate || 0.05;
  const gst = afterCommission * gstRate;
  const grandTotal = afterCommission + gst;

  return {
    familyType,
    roomCalculation,
    hotelCosts,
    additionalCosts,
    vehicleCosts,
    finalCalculation: {
      subtotal: Math.round(subtotal),
      discount: Math.round(discount),
      afterDiscount: Math.round(afterDiscount),
      commission: Math.round(commission),
      afterCommission: Math.round(afterCommission),
      gst: Math.round(gst),
      grandTotal: Math.round(grandTotal)
    }
  };
};
