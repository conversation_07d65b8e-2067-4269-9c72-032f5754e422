import React, { useState, useEffect, useRef } from 'react';

const PACKAGE_TYPES = [
  { value: 'Solo', label: 'Solo' },
  { value: 'Couples', label: 'Couples' },
  { value: 'Honeymoon', label: 'Honeymoon' },
  { value: 'Family', label: 'Family' },
  { value: 'Group', label: 'Group' },
  { value: 'Worship-Religious', label: 'Worship - Religious' },
  { value: 'Pocket-Friendly', label: 'Pocket Friendly' },
  { value: 'Silver', label: 'Silver' },
  { value: 'Gold', label: 'Gold' },
  { value: 'Platinum', label: 'Platinum' },
  { value: 'Diamond', label: 'Diamond' },
  { value: 'Family-Budget', label: 'Family - Budget' },
  { value: 'Family-Group', label: 'Family - Group' },
  { value: 'Family-Silver', label: 'Family - Silver' },
  { value: 'Family-Gold', label: 'Family - Gold' },
  { value: 'Family-Platinum', label: 'Family - Platinum' },
  { value: 'Couple-Budget', label: 'Couple - Budget' },
  { value: 'Couple-Platinum', label: 'Couple - Platinum' },
  { value: 'Couple-Premium', label: 'Couple - Premium' },
  { value: 'Couple-Luxury', label: 'Couple - Luxury' },
  { value: 'Honeymoon-Budget', label: 'Honeymoon - Budget' },
  { value: 'Honeymoon-Platinum', label: 'Honeymoon - Platinum' },
  { value: 'Honeymoon-Premium', label: 'Honeymoon - Premium' },
  { value: 'Honeymoon-Luxury', label: 'Honeymoon - Luxury' },
  { value: 'Group-Budget', label: 'Group - Budget' },
  { value: 'Group-Platinum', label: 'Group - Platinum' },
  { value: 'Group-Premium', label: 'Group - Premium' },
  { value: 'Group-Luxury', label: 'Group - Luxury' }
];

interface Props {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const PackageTypeAutocomplete: React.FC<Props> = ({
  value,
  onChange,
  placeholder = "Enter package type"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (inputValue: string) => {
    onChange(inputValue);
    if (inputValue.trim() === '') {
      setSuggestions(PACKAGE_TYPES.map(type => type.label));
    } else {
      const filtered = PACKAGE_TYPES.filter(type =>
        type.label.toLowerCase().includes(inputValue.toLowerCase())
      ).map(type => type.label);
      setSuggestions(filtered);
    }
    setIsOpen(true);
  };

  const handleFocus = () => {
    setSuggestions(PACKAGE_TYPES.map(type => type.label));
    setIsOpen(true);
  };

  return (
    <div ref={wrapperRef} className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        onFocus={handleFocus}
        placeholder={placeholder}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
      {isOpen && (
        <ul className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {suggestions.map((suggestion, index) => (
            <li
              key={index}
              onClick={() => {
                onChange(suggestion);
                setIsOpen(false);
              }}
              className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
            >
              {suggestion}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
