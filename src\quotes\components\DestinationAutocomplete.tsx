import React, { useState, useEffect, useRef } from 'react';

const Destination = [
  "Agra", "Ahmedabad", "Airport Road", "Ajmer", "Aligarh", "Alleppey", "Amritsar", "Andaman", "Aurangabad",
  "Badami", "Baddi", "Bagalkot", "Bangalore", "Bareilly", "Belagavi", "Bellandur", "Bengaluru", "Bharatpur",
  "Bharuch", "Bhopal", "Bhuj", "Bikaner", "Bommasandra", "Brookfield", "Chandigarh", "Chaukori", "Chennai",
  "Cherai", "Cherrapunjee", "Cochin", "Coimbatore", "Coorg", "Corbett", "Dahej", "Dalhousie", "Darjeeling",
  "Davangere", "Dechu", "Dehradun", "Delhi", "Devala", "Dharamshala", "Dudhwa", "Dwaki", "Fort Cochin",
  "Gadag", "Gangtok", "Goa", "Gulmarg", "Gurugram", "Gwalior", "<PERSON><PERSON>", "<PERSON>d<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>", "Hyderabad", "Indira Nagar", "Indore", "Jaipur", "Jalandhar", "Jammu", "Jodhpur", "Kabini",
  "Kangra", "Kanpur", "Kanthalloor", "Kanyakumari", "Kasarwadi", "Kashmir", "Katra", "Kedarnath", "Kerela",
  "Kochi", "Kodaikanal", "Kolkata", "Kota", "Kovalam", "Kumarakom", "Kurnool", "Kurukshetra", "Ladakh",
  "Leh", "Lonavala", "Lucknow", "Ludhiana", "Madurai", "Mahabaleshwar", "Mahape", "Maheshwar", "Manali",
  "Mandi", "Mandya", "Meghalaya", "Moradabad", "Morbi", "Muhamma", "Mumbai", "Munnar", "Mussoorie", "Mysore",
  "Nagpur", "Nahan", "Nainital", "Nashik", "Navi Mumbai", "Noida", "Ooty", "Pahalgam", "Patna", "Pelling",
  "Pench", "Pondicherry", "Poovar", "Port Blair", "Pozhiyoor", "Pune", "Pushkar", "Raebareily", "Raipur",
  "Rajgarh", "Rajkot", "Rameswaram", "Ramgarh", "Ranthambore", "Rishikesh", "Sambalpur", "Shillong",
  "Shimla", "Shimoga", "Shivpora", "Siliguri", "Somnath", "Sonamarg", "Sonmarg", "Srinagar", "Thekkady",
  "Trivandrum", "Udaipur", "Vadodara", "Vagamon", "Vapi", "Varanasi", "Varkala", "Viman Nagar", "Wayanad",
  "Yercaud", "Zirakpur"
];

interface DestinationAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const DestinationAutocomplete: React.FC<DestinationAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Enter Destination name"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (inputValue: string) => {
    onChange(inputValue);
    if (inputValue.trim() === '') {
      setSuggestions(Destination); // Show all destinations when input is empty
    } else {
      const filtered = Destination.filter(name =>
        name.toLowerCase().includes(inputValue.toLowerCase())
      );
      setSuggestions(filtered);
    }
    setIsOpen(true);
  };

  const handleFocus = () => {
    setSuggestions(Destination); // Show all destinations on focus
    setIsOpen(true);
  };

  return (
    <div ref={wrapperRef} className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        onFocus={handleFocus}
        autoComplete="off"
        placeholder={placeholder}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
      {isOpen && (
        <ul className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {suggestions.map((suggestion, index) => (
            <li
              key={index}
              onClick={() => {
                onChange(suggestion);
                setIsOpen(false);
              }}
              className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
            >
              {suggestion}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
