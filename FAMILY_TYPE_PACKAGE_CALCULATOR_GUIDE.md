# Family Type Package Cost Calculator - User Guide

## Overview

The Family Type Package Cost Calculator now provides **accurate package pricing** for all family types based on Quote Mapping data. This gives you precise costs that follow the same calculation logic as the Quote Generator.

## 🚀 New Features Added

### **1. Package Cost Calculation**
- **Uses Quote Mapping data** for accurate pricing
- **Follows Quote Generator logic** exactly
- **Detailed cost breakdown** for each family type
- **Room cost calculation** based on occupancy rules
- **Vehicle cost optimization** based on family size
- **Additional costs integration** (meals, ferry, activities, guide, parking/toll)

### **2. Enhanced UI Display**
- **Package Cost Cards** with detailed breakdowns
- **Comparison Tables** for easy analysis
- **Color-coded pricing** (Green for Package Costs, Blue for Basic Estimates)
- **Per-person cost calculation**
- **Room and occupancy details**

## 📋 How to Use

### **Step 1: Prerequisites**
1. **Create a Quote** in Quote Generator (e.g., "Tropical Paradise in Andaman" for ₹43,630)
2. **Save the Quote** to database (not as draft)
3. **Map the Quote** in Quote Mapping tab:
   - Set hotel extra adult/children costs
   - Configure vehicle pricing
   - Set additional costs (meals, ferry, activities, etc.)

### **Step 2: Calculate Package Costs**
1. **Go to Family Type tab**
2. **Select your baseline quote** from dropdown
3. **Click "Calculate Package Costs (Using Quote Mapping)"**
4. **Wait for calculation** to complete

### **Step 3: View Results**
- **Family Type Cards** show detailed package costs
- **Package Cost Table** provides comparison view
- **Detailed breakdowns** show cost components

## 💰 Package Cost Calculation Logic

### **Room Cost Calculation**
```
Standard Room Occupancy: 2 Adults + 2 Children (≤5) OR 1 Extra Adult
Family Room Occupancy: 4 Adults + 1 Extra Adult

Room Requirements:
- Rooms Needed = ceil(Adult Equivalent / 3)
- Adult Equivalent = Adults + (Children 6-12 × 0.5)
- Extra Adults = Adults - (Rooms × 2)
```

### **Cost Components**
1. **Base Room Cost**: Nights × Rooms × Room Rate
2. **Extra Adult Cost**: Extra Adults × Cost per Night × Nights
3. **Children Cost**: Children (6-12) × Cost per Night × Nights
4. **Infant Cost**: Infants × Cost per Night × Nights (usually ₹0)
5. **Vehicle Cost**: Based on family size and cab type
6. **Additional Costs**: Meals + Ferry + Activities + Guide + Parking/Toll
7. **Commission**: Configurable percentage
8. **GST**: Applied on final amount

### **Age Group Rules**
- **Infants (≤2 years)**: Usually free
- **Children (2-5 years)**: Free accommodation, may have meal costs
- **Children (6-12 years)**: Charged as per hotel policy
- **Adults**: Full cost
- **Grandparents**: May have discounts (configurable)

## 📊 Understanding the Results

### **Package Cost Card Display**
```
Package Cost: ₹52,450
Based on Quote Mapping data

Package Breakdown:
Room Cost: ₹25,000
Extra Adults: ₹3,000
Children (6-12): ₹2,100
Vehicle Cost: ₹8,500
Additional Costs: ₹5,200
Commission: ₹2,200
GST: ₹6,450

Rooms: 2 | Extra Adults: 1
Children (6-12): 1 | Infants Free: 1
```

### **Comparison Table Columns**
- **Family Type**: Name and ID
- **Composition**: Adults, Children, Infants count
- **Rooms**: Required rooms + extra adults
- **Room Cost**: Total accommodation cost
- **Vehicle Cost**: Transportation cost
- **Additional**: Other costs + commission + GST
- **Total Package**: Final package cost
- **Per Person**: Cost divided by total members

## 🔄 Calculation Examples

### **Example 1: Tiny Delight (2A + 1C≤5)**
```
Baseline: ₹43,630 for 2A + 1C≤5
Target: Same family type
Result: ₹43,630 (same as baseline)
```

### **Example 2: Family Adventure (4A + 2C6-12)**
```
Baseline: ₹43,630 for 2A + 1C≤5
Target: 4A + 2C6-12

Calculation:
- Rooms: 2 (4 adults need 2 rooms)
- Extra Adults: 0 (4 adults fit in 2 rooms)
- Children Cost: 2 × ₹1,400 × 5 nights = ₹14,000
- Vehicle: Innova (20% increase) = ₹8,500 × 1.2 = ₹10,200
- Additional Costs: Scaled for 6 people vs 3 people
- Result: ~₹78,500
```

## 🎯 Benefits of Package Cost Calculator

### **Accuracy**
- **Uses actual hotel rates** from Quote Mapping
- **Precise vehicle costs** based on capacity needs
- **Real additional costs** (meals, activities, etc.)
- **Follows Quote Generator logic** exactly

### **Transparency**
- **Detailed cost breakdown** for each component
- **Room occupancy details** clearly shown
- **Extra charges** explicitly calculated
- **Commission and GST** separately displayed

### **Business Intelligence**
- **Price comparison** across family types
- **Per-person cost analysis**
- **Profit margin visibility**
- **Competitive pricing insights**

## 🔧 Troubleshooting

### **"Quote mapping data not found" Error**
1. Go to Quote Mapping tab
2. Select the same quote
3. Ensure hotel mappings are configured
4. Save the mapping data
5. Return to Family Type tab and retry

### **Inaccurate Costs**
1. Verify hotel extra adult/children costs in Quote Mapping
2. Check vehicle pricing configuration
3. Ensure additional costs are properly set
4. Verify baseline quote has correct total cost

### **Missing Family Types**
1. Check Family Type database table
2. Ensure family types have proper cab_type and rooms_need
3. Verify family_count is correctly calculated

## 📈 Best Practices

1. **Always use Package Cost** calculation for accurate pricing
2. **Keep Quote Mapping data updated** with current hotel rates
3. **Review vehicle costs** regularly for market changes
4. **Set realistic additional costs** based on actual expenses
5. **Use comparison tables** for competitive analysis
6. **Export results** for client presentations

## 🔮 Future Enhancements

- **Seasonal pricing** adjustments
- **Dynamic vehicle selection** based on availability
- **Bulk discount calculations** for large groups
- **Integration with booking systems**
- **Real-time rate updates** from hotel APIs

The Package Cost Calculator provides the most accurate pricing available, ensuring your quotes are competitive and profitable!
