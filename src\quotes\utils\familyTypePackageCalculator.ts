import { supabase } from '../lib/supabaseClient';

// Enhanced interfaces for package calculation
interface QuoteMappingData {
  id: string;
  quote_id: string;
  quote_name: string;
  customer_name: string;
  destination: string;
  hotel_mappings: HotelCostMapping[];
  vehicle_mappings: VehicleCostMapping[];
  additional_costs: AdditionalCosts;
  created_at: string;
  updated_at: string;
}

interface HotelCostMapping {
  hotel_name: string;
  extra_adult_cost: number;
  children_cost: number;
  infant_cost: number;
  grandparent_discount: number;
}

interface VehicleCostMapping {
  vehicle_type: string;
  pricing_type: 'multiplier' | 'actual_cost';
  base_cost: number;
  cost_multiplier: number;
  max_capacity: number;
  is_active: boolean;
}

interface AdditionalCosts {
  meal_cost_per_person: number;
  ferry_cost: number;
  activity_cost_per_person: number;
  guide_cost_per_day: number;
  parking_toll_multiplier: number;
}

interface BaselineQuoteData {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  no_of_persons: number;
  children: number;
  infants: number;
  extra_adults: number;
  total_cost: number;
  nights: number;
  commission_rate?: number;
  discount_amount?: number;
  gst_rate?: number;
}

interface FamilyType {
  id: string;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;
  no_of_child: number;
  no_of_children: number;
  family_count: number;
  cab_type: string;
  cab_capacity: number;
  rooms_need: number;
}

interface PackageCostBreakdown {
  roomCost: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  vehicleCost: number;
  additionalCosts: number;
  subtotal: number;
  commission: number;
  gst: number;
  totalPackageCost: number;
  roomsNeeded: number;
  extraAdultsCharged: number;
  childrenCharged: number;
  infantsIncluded: number;
}

// Calculate room requirements based on family composition
export const calculateRoomRequirements = (familyType: FamilyType) => {
  const adults = familyType.no_of_adults;
  const children = (familyType.no_of_child || 0) + (familyType.no_of_children || 0);
  const infants = familyType.no_of_infants || 0;
  
  // Standard room occupancy: 2 adults + 2 children under 5 (or 1 extra adult)
  // Children 6-12 count as 0.5 adult for room calculation
  const childrenAsAdults = (familyType.no_of_children || 0) * 0.5;
  const totalAdultEquivalent = adults + childrenAsAdults;
  
  // Calculate rooms needed (2 adults per room, with 1 extra adult allowed)
  const roomsNeeded = Math.ceil(totalAdultEquivalent / 3); // 2 + 1 extra adult per room
  
  // Calculate extra adults (beyond 2 per room)
  const baseAdultsInRooms = roomsNeeded * 2;
  const extraAdults = Math.max(0, adults - baseAdultsInRooms);
  
  return {
    roomsNeeded,
    extraAdults,
    childrenCharged: familyType.no_of_children || 0, // Children 6-12
    childrenFree: familyType.no_of_child || 0, // Children under 5
    infantsIncluded: infants
  };
};

// Get vehicle cost multiplier based on family size and cab type
export const getVehicleCostMultiplier = (
  familyType: FamilyType, 
  vehicleMappings: VehicleCostMapping[]
): { multiplier: number; vehicleType: string; cost: number } => {
  const totalMembers = familyType.family_count;
  const cabType = familyType.cab_type?.toLowerCase() || '';
  
  // Find matching vehicle mapping
  const matchingVehicle = vehicleMappings.find(vm => 
    vm.is_active && 
    vm.max_capacity >= totalMembers &&
    vm.vehicle_type.toLowerCase().includes(cabType.split(' ')[0])
  );
  
  if (matchingVehicle) {
    if (matchingVehicle.pricing_type === 'actual_cost') {
      return {
        multiplier: 1.0,
        vehicleType: matchingVehicle.vehicle_type,
        cost: matchingVehicle.base_cost
      };
    } else {
      return {
        multiplier: matchingVehicle.cost_multiplier,
        vehicleType: matchingVehicle.vehicle_type,
        cost: 0 // Will be calculated based on baseline
      };
    }
  }
  
  // Fallback to default multipliers
  if (totalMembers <= 4) return { multiplier: 1.0, vehicleType: 'Sedan', cost: 0 };
  if (totalMembers <= 7) return { multiplier: 1.2, vehicleType: 'Innova', cost: 0 };
  if (totalMembers <= 12) return { multiplier: 1.4, vehicleType: 'Tempo Traveller', cost: 0 };
  return { multiplier: 1.6, vehicleType: 'Mini Bus', cost: 0 };
};

// Calculate package cost for a family type using quote mapping data
export const calculateFamilyTypePackageCost = (
  familyType: FamilyType,
  baselineQuote: BaselineQuoteData,
  quoteMapping: QuoteMappingData
): PackageCostBreakdown => {
  
  // Calculate room requirements
  const roomReq = calculateRoomRequirements(familyType);
  
  // Calculate base room cost (from baseline quote)
  const baselineRooms = Math.ceil((baselineQuote.no_of_persons + baselineQuote.extra_adults) / 2);
  const baseRoomCostPerRoom = baselineQuote.total_cost * 0.4 / baselineRooms; // Assume 40% of total cost is room cost
  const roomCost = baseRoomCostPerRoom * roomReq.roomsNeeded;
  
  // Calculate extra costs using hotel mappings
  let extraAdultCost = 0;
  let childrenCost = 0;
  let infantCost = 0;
  
  if (quoteMapping.hotel_mappings.length > 0) {
    const avgHotelMapping = quoteMapping.hotel_mappings[0]; // Use first hotel for calculation
    extraAdultCost = avgHotelMapping.extra_adult_cost * roomReq.extraAdults * (baselineQuote.nights || 1);
    childrenCost = avgHotelMapping.children_cost * roomReq.childrenCharged * (baselineQuote.nights || 1);
    infantCost = avgHotelMapping.infant_cost * roomReq.infantsIncluded * (baselineQuote.nights || 1);
  }
  
  // Calculate vehicle cost
  const vehicleInfo = getVehicleCostMultiplier(familyType, quoteMapping.vehicle_mappings);
  const baseVehicleCost = baselineQuote.total_cost * 0.25; // Assume 25% of total cost is vehicle cost
  const vehicleCost = vehicleInfo.cost > 0 ? vehicleInfo.cost : baseVehicleCost * vehicleInfo.multiplier;
  
  // Calculate additional costs
  const additionalCosts = 
    (quoteMapping.additional_costs.meal_cost_per_person * familyType.family_count) +
    quoteMapping.additional_costs.ferry_cost +
    (quoteMapping.additional_costs.activity_cost_per_person * familyType.family_count) +
    (quoteMapping.additional_costs.guide_cost_per_day * (baselineQuote.nights || 1)) +
    (baselineQuote.total_cost * 0.1 * quoteMapping.additional_costs.parking_toll_multiplier); // 10% for parking/toll
  
  // Calculate subtotal
  const subtotal = roomCost + extraAdultCost + childrenCost + infantCost + vehicleCost + additionalCosts;
  
  // Apply discount if any
  const discountAmount = baselineQuote.discount_amount || 0;
  const afterDiscount = Math.max(0, subtotal - discountAmount);
  
  // Calculate commission
  const commissionRate = baselineQuote.commission_rate || 5; // Default 5%
  const commission = afterDiscount * (commissionRate / 100);
  const afterCommission = afterDiscount + commission;
  
  // Calculate GST
  const gstRate = baselineQuote.gst_rate || 0.05; // Default 5%
  const gst = afterCommission * gstRate;
  const totalPackageCost = afterCommission + gst;
  
  return {
    roomCost: Math.round(roomCost),
    extraAdultCost: Math.round(extraAdultCost),
    childrenCost: Math.round(childrenCost),
    infantCost: Math.round(infantCost),
    vehicleCost: Math.round(vehicleCost),
    additionalCosts: Math.round(additionalCosts),
    subtotal: Math.round(subtotal),
    commission: Math.round(commission),
    gst: Math.round(gst),
    totalPackageCost: Math.round(totalPackageCost),
    roomsNeeded: roomReq.roomsNeeded,
    extraAdultsCharged: roomReq.extraAdults,
    childrenCharged: roomReq.childrenCharged,
    infantsIncluded: roomReq.infantsIncluded
  };
};

// Fetch quote mapping data for a specific quote
export const fetchQuoteMappingData = async (quoteId: string): Promise<QuoteMappingData | null> => {
  try {
    const { data, error } = await supabase
      .from('quote_mappings')
      .select('*')
      .eq('quote_id', quoteId)
      .single();
    
    if (error) {
      console.error('Error fetching quote mapping:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in fetchQuoteMappingData:', error);
    return null;
  }
};

// Fetch baseline quote data
export const fetchBaselineQuoteData = async (quoteId: string): Promise<BaselineQuoteData | null> => {
  try {
    const { data, error } = await supabase
      .from('quotes')
      .select(`
        id,
        package_name,
        customer_name,
        destination,
        family_type,
        no_of_persons,
        children,
        infants,
        extra_adults,
        total_cost,
        nights,
        commission_rate,
        discount_amount,
        gst_rate
      `)
      .eq('id', quoteId)
      .single();
    
    if (error) {
      console.error('Error fetching baseline quote:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in fetchBaselineQuoteData:', error);
    return null;
  }
};

// Format price for display
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};
