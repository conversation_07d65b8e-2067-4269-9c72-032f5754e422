import React, { useState, useMemo, useEffect } from 'react';
import { <PERSON>, Baby, Shield as Child, UserPlus, Car, Search, Filter, Home, ChevronDown, Calculator, DollarSign, Download, BarChart3, Info, Package, TrendingUp } from 'lucide-react';
import { supabase } from '../lib/supabaseClient';
import { getQuoteSupabaseConfig, getCrmSupabaseConfig } from '../../config/env';
import { createClient } from '@supabase/supabase-js';
import { exportFamilyTypePricing, calculatePricingInsights } from '../utils/familyPriceExport';
import { fetchCompleteQuoteData, calculateFamilyTypePriceDetailed } from '../utils/familyPriceCalculator';
import {
  calculateFamilyTypePackageCost,
  fetchQuoteMappingData,
  fetchBaselineQuoteData,
  formatPrice
} from '../utils/familyTypePackageCalculator';
import {
  fetchFamilyTypesFromDB,
  calculateEnhancedFamilyTypePackage,
  calculateFamilyRoomRequirements
} from '../utils/enhancedFamilyTypeCalculator';

const FAMILY_OPTIONS = {
  adults: [1, 2, 3, 4, 5, 6],
  infants: [0, 1, 2, 3],
  children: [0, 1, 2, 3, 4],
  teenagers: [0, 1, 2, 3, 4],
  grandparents: [0, 1, 2, 3, 4],
  rooms: [1, 2, 3, 4, 5],
  cabTypes: [
    'Sedan - 4 Seater',
    'Toyota Innova AC',
    'SUV',
    'Tempo Traveller',
    'Mini Bus'
  ]
};

interface FamilyType {
  id: string;
  name: string;
  description: string;
  adults: number;
  infants: number;
  children: number;
  teenagers: number;
  grandparents: number;
  totalCount: number;
  cabType: string;
  rooms: number;
  family_id?: string;
  family_type?: string;
  no_of_adults?: number;
  no_of_infants?: number;
  no_of_child?: number;
  no_of_children?: number;
  family_count?: number;
  cab_type?: string;
  cab_capacity?: number;
  rooms_need?: number;
  calculatedPrice?: number;
  packageCost?: number;
  cabValidation?: {
    isValid: boolean;
    message: string;
    capacityStatus: string;
  };
  priceBreakdown?: {
    hotelCost: number;
    otherCosts: number;
    commission: number;
    gst: number;
    discount: number;
    roomsNeeded: number;
    extraAdults: number;
    childrenCharged: number;
    infantsFree: number;
  };
  packageBreakdown?: {
    roomCost: number;
    extraAdultCost: number;
    childrenCost: number;
    infantCost: number;
    vehicleCost: number;
    additionalCosts: number;
    subtotal: number;
    commission: number;
    gst: number;
    totalPackageCost: number;
    roomsNeeded: number;
    extraAdultsCharged: number;
    childrenCharged: number;
    infantsIncluded: number;
  };
}

interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  total_cost: number;
  no_of_persons: number;
  children: number;
  infants: number;
  extra_adults: number;
  is_draft: boolean;
}

// Default family types (fallback if Supabase data is not available)
const defaultFamilyTypes: FamilyType[] = [
  {
    id: 'BB-1',
    name: 'Baby Bliss',
    description: '2 Adults + 1 Infant (Below 2 yrs)',
    adults: 2,
    infants: 1,
    children: 0,
    teenagers: 0,
    grandparents: 0,
    totalCount: 3,
    cabType: 'Sedan - 4 Seater',
    rooms: 1
  },
  {
    id: 'TD-2',
    name: 'Tiny Delight',
    description: '2 Adults + 1 Child (Below 5 yrs)',
    adults: 2,
    infants: 0,
    children: 1,
    teenagers: 0,
    grandparents: 0,
    totalCount: 3,
    cabType: 'Sedan - 4 Seater',
    rooms: 1
  },
  {
    id: 'GB-15',
    name: 'Grand Bliss',
    description: '2 Adults + 1 Infant (Below 2 yrs) + 2 Grandparents',
    adults: 2,
    infants: 1,
    children: 0,
    teenagers: 0,
    grandparents: 2,
    totalCount: 5,
    cabType: 'Toyota Innova AC',
    rooms: 2
  }
];

// Create clients outside component to avoid recreation on every render
const crmConfig = getCrmSupabaseConfig();
const quoteConfig = getQuoteSupabaseConfig();

// CRM client for family types (has 34 family types)
const crmSupabase = createClient(crmConfig.url, crmConfig.anonKey, {
  auth: {
    storageKey: 'tripxplo-crm-auth', // Different storage key to avoid conflicts
    persistSession: true,
    autoRefreshToken: true,
  }
});

// Quote client for quotes (has the actual quotes) - now using environment config
const quoteSupabase = createClient(quoteConfig.url, quoteConfig.anonKey, {
  auth: {
    storageKey: 'tripxplo-quote-auth', // Different storage key to avoid conflicts
    persistSession: true,
    autoRefreshToken: true,
  }
});

function FamilyTypeTab() {
  const [selectedFamily, setSelectedFamily] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [familyTypes, setFamilyTypes] = useState<FamilyType[]>(defaultFamilyTypes);

  // Debug clients on component load (only log once)
  React.useEffect(() => {
    console.log('🔍 FamilyTypeTab component loaded');
    console.log('📊 CRM Supabase client available:', !!crmSupabase);
    console.log('📊 Quote Supabase client available:', !!quoteSupabase);
    console.log('🔗 CRM Database URL:', crmConfig.url);
    console.log('🔗 Quote Database URL:', quoteConfig.url);
  }, []);
  const [baselineQuotes, setBaselineQuotes] = useState<BaselineQuote[]>([]);
  const [selectedBaselineQuote, setSelectedBaselineQuote] = useState<string>('');
  const [isLoadingFamilyTypes, setIsLoadingFamilyTypes] = useState(false);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState(false);
  const [showPriceCalculation, setShowPriceCalculation] = useState(false);
  const [includeDrafts, setIncludeDrafts] = useState(true);
  const [isCalculatingPackageCosts, setIsCalculatingPackageCosts] = useState(false);
  const [showPackageCalculation, setShowPackageCalculation] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [filters, setFilters] = useState({
    maxMembers: 'all',
    vehicleType: 'all',
    hasInfants: false,
    hasGrandparents: false
  });

  const [newFamilyType, setNewFamilyType] = useState({
    adults: 2,
    infants: 0,
    children: 0,
    teenagers: 0,
    grandparents: 0,
    rooms: 1,
    cabType: FAMILY_OPTIONS.cabTypes[0]
  });

  // Fetch family types from CRM and quotes from Quote database
  useEffect(() => {
    console.log('🔄 useEffect triggered for data fetch');
    console.log('📊 CRM client status:', crmSupabase ? 'Available' : 'Not available');
    console.log('📊 Quote client status:', quoteSupabase ? 'Available' : 'Not available');
    fetchFamilyTypes();
    fetchBaselineQuotes();
  }, [crmSupabase, quoteSupabase]);

  // Initial load happens in the main useEffect above

  // Refetch baseline quotes when includeDrafts toggle changes
  useEffect(() => {
    if (quoteSupabase) {
      fetchBaselineQuotes();
    }
  }, [includeDrafts, quoteSupabase]);

  // Removed auto-refresh - users can refresh manually using the buttons

  // Removed real-time subscription - users can refresh manually using the buttons

  // Enhanced function to validate cab capacity against family count
  const validateCabCapacity = (familyCount: number, cabCapacity: number, cabType: string) => {
    const isValid = cabCapacity >= familyCount;
    return {
      isValid,
      message: isValid
        ? `✓ ${cabType} (${cabCapacity} seats) fits ${familyCount} members`
        : `⚠ ${cabType} (${cabCapacity} seats) insufficient for ${familyCount} members`,
      capacityStatus: isValid ? 'adequate' : 'insufficient'
    };
  };

  // Test database connections and table structures
  const testDatabaseConnection = async () => {
    try {
      console.log('🧪 Testing database connections...');
      let results = [];

      // Test CRM database (family types)
      if (crmSupabase) {
        console.log('🔍 Testing CRM database for family types...');
        const { count: familyCount, error: familyError } = await crmSupabase
          .from('family_type')
          .select('*', { count: 'exact', head: true });

        if (familyError) {
          console.error('❌ CRM database test failed:', familyError);
          results.push(`❌ CRM DB: ${familyError.message}`);
        } else {
          console.log('✅ CRM database connection successful');
          console.log('📊 Total family types:', familyCount);
          results.push(`✅ CRM DB: ${familyCount} family types`);
        }
      } else {
        results.push('❌ CRM DB: Client not available');
      }

      // Test Quote database (quotes)
      if (quoteSupabase) {
        console.log('🔍 Testing Quote database for quotes...');
        const { count: quoteCount, error: quoteError } = await quoteSupabase
          .from('quotes')
          .select('*', { count: 'exact', head: true });

        if (quoteError) {
          console.error('❌ Quote database test failed:', quoteError);
          results.push(`❌ Quote DB: ${quoteError.message}`);
        } else {
          console.log('✅ Quote database connection successful');
          console.log('📊 Total quotes:', quoteCount);
          results.push(`✅ Quote DB: ${quoteCount} quotes`);
        }
      } else {
        results.push('❌ Quote DB: Client not available');
      }

      // Show results
      alert(`Database Test Results:\n\n${results.join('\n')}`);

      // Refresh data if tests passed
      console.log('🔄 Refreshing data...');
      await fetchFamilyTypes();
      await fetchBaselineQuotes();

    } catch (error) {
      console.error('❌ Database test exception:', error);
      alert(`❌ Database test exception: ${error.message}`);
    }
  };

  const fetchFamilyTypes = async () => {
    setIsLoadingFamilyTypes(true);
    try {
      if (!crmSupabase) {
        console.error('❌ CRM Supabase client not available');
        console.log('🔄 Using default family types as fallback');
        setFamilyTypes(defaultFamilyTypes);
        return;
      }

      console.log('🔍 Fetching family types from CRM database...');
      console.log('📊 CRM Database connection status:', crmSupabase ? 'Connected' : 'Not connected');

      const { data, error } = await crmSupabase
        .from('family_type')
        .select('*')
        .order('family_id');

      console.log('📋 Raw database response:', { data, error, dataLength: data?.length });

      if (error) {
        console.error('❌ Error fetching family types:', error);
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        console.log('🔄 Using default family types as fallback');
        alert(`❌ Error fetching family types: ${error.message}`);
        setFamilyTypes(defaultFamilyTypes);
        return;
      }

      if (data && data.length > 0) {
        console.log(`✅ Found ${data.length} family types in database`);
        console.log('📊 Sample family type data:', data[0]);

        // Transform Supabase data to match our interface with enhanced validation
        const transformedFamilyTypes: FamilyType[] = data.map(item => {
          const familyCount = item.family_count || 0;
          const cabCapacity = item.cab_capacity || 0;
          const cabType = item.cab_type || 'Standard';

          // Validate cab capacity
          const cabValidation = validateCabCapacity(familyCount, cabCapacity, cabType);

          // Combine child fields properly
          const totalChildren = (item.no_of_child || 0) + (item.no_of_children || 0);

          // Create enhanced description with capacity validation
          const baseDescription = item.family_type || 'Unknown Family Type';
          const capacityInfo = cabValidation.isValid ? '' : ' (⚠ Cab undersized)';

          return {
            id: item.family_id || `FT-${Math.random().toString(36).substr(2, 9)}`,
            name: item.family_type || 'Unknown Family Type',
            description: `${baseDescription}${capacityInfo}`,
            adults: item.no_of_adults || 0,
            infants: item.no_of_infants || 0,
            children: totalChildren,
            teenagers: 0, // Not available in current schema
            grandparents: 0, // Not available in current schema
            totalCount: familyCount,
            cabType: cabType,
            rooms: item.rooms_need || 1,
            // Keep original data for reference
            family_id: item.family_id,
            family_type: item.family_type,
            no_of_adults: item.no_of_adults,
            no_of_infants: item.no_of_infants,
            no_of_child: item.no_of_child,
            no_of_children: item.no_of_children,
            family_count: item.family_count,
            cab_type: item.cab_type,
            cab_capacity: item.cab_capacity,
            rooms_need: item.rooms_need,
            // Add validation info
            cabValidation: cabValidation
          };
        });

        console.log(`✅ Successfully loaded ${transformedFamilyTypes.length} family types from CRM database`);
        console.log('📊 First few family types:', transformedFamilyTypes.slice(0, 3).map(ft => ft.name));

        // Log capacity validation summary
        const validCabs = transformedFamilyTypes.filter(ft => ft.cabValidation?.isValid).length;
        const invalidCabs = transformedFamilyTypes.length - validCabs;
        console.log(`Cab capacity validation: ${validCabs} valid, ${invalidCabs} undersized`);

        setFamilyTypes(transformedFamilyTypes);
        setLastUpdated(new Date());

        console.log('🔄 Family types state updated, current count:', transformedFamilyTypes.length);

        // Auto-calculate prices if we have a selected baseline quote
        if (selectedBaselineQuote && baselineQuotes.length > 0) {
          console.log('🔄 Auto-calculating prices for newly loaded family types...');
          setTimeout(() => {
            calculateAllPrices();
          }, 1000); // Small delay to ensure state is updated
        }
      } else {
        console.log('⚠️ No family types found in database, using default family types');
        console.log('📊 Database returned empty result set');
        setFamilyTypes(defaultFamilyTypes);
      }
    } catch (error) {
      console.error('❌ Exception while fetching family types:', error);
      console.error('❌ Exception details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      console.log('🔄 Using default family types as fallback');
      setFamilyTypes(defaultFamilyTypes);
    } finally {
      setIsLoadingFamilyTypes(false);
    }
  };

  const fetchBaselineQuotes = async () => {
    setIsLoadingQuotes(true);
    try {
      if (!quoteSupabase) {
        console.error('Quote Supabase client not available');
        return;
      }

      console.log('🔍 Fetching quotes from Quote database...');

      // Use the correct schema for the Quote database
      let query = quoteSupabase
        .from('quotes')
        .select('id, package_name, customer_name, destination, family_type, total_cost, no_of_persons, children, infants, extra_adults, is_draft')
        .not('total_cost', 'is', null)
        .gt('total_cost', 0)
        .order('created_at', { ascending: false })
        .limit(20);

      // Only filter out drafts if includeDrafts is false
      if (!includeDrafts) {
        query = query.eq('is_draft', false);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching baseline quotes:', error);
        return;
      }

      console.log('✅ Successfully fetched quotes from Quote database:', data?.length || 0);
      setBaselineQuotes(data || []);
    } catch (error) {
      console.error('Exception fetching baseline quotes:', error);
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  // Calculate price for a family type based on baseline quote
  const calculateFamilyTypePrice = (familyType: FamilyType, baselineQuote: BaselineQuote): number => {
    if (!baselineQuote || !baselineQuote.total_cost) return 0;

    // Get baseline family composition
    const baselineAdults = (baselineQuote.no_of_persons || 0) + (baselineQuote.extra_adults || 0);
    const baselineChildren = baselineQuote.children || 0;
    const baselineInfants = baselineQuote.infants || 0;
    const baselineTotalPax = baselineAdults + baselineChildren + baselineInfants;

    // Ensure baseline has at least some people
    if (baselineTotalPax === 0) return 0;

    // Get target family composition
    const targetAdults = familyType.adults || familyType.no_of_adults || 0;
    const targetChildren = familyType.children || familyType.no_of_children || familyType.no_of_child || 0;
    const targetInfants = familyType.infants || familyType.no_of_infants || 0;
    const targetTotalPax = targetAdults + targetChildren + targetInfants;

    // Ensure target family has at least some people
    if (targetTotalPax === 0) return 0;

    // Base price per adult from baseline (using adults as base unit)
    const basePricePerAdult = baselineAdults > 0 ? baselineQuote.total_cost / baselineAdults : baselineQuote.total_cost / baselineTotalPax;

    // Calculate pricing factors
    let adultPrice = basePricePerAdult * targetAdults;
    let childPrice = basePricePerAdult * 0.7 * targetChildren; // 70% of adult price for children
    let infantPrice = basePricePerAdult * 0.1 * targetInfants; // 10% of adult price for infants

    // Room factor adjustment
    const baselineRooms = Math.max(1, Math.ceil(baselineAdults / 2)); // Assume 2 adults per room, minimum 1 room
    const targetRooms = familyType.rooms || familyType.rooms_need || Math.max(1, Math.ceil(targetAdults / 2));
    const roomFactor = targetRooms / baselineRooms;

    // Apply room factor to accommodation portion (assume 40% of total cost is accommodation)
    const baseTotal = adultPrice + childPrice + infantPrice;
    const accommodationPortion = baseTotal * 0.4;
    const otherPortion = baseTotal * 0.6;
    
    const adjustedAccommodation = accommodationPortion * roomFactor;
    const totalPrice = adjustedAccommodation + otherPortion;

    // Add transportation cost adjustment based on cab type
    let transportationMultiplier = 1.0;
    const familyCabType = (familyType.cabType || familyType.cab_type || '').toLowerCase();
    
    if (familyCabType.includes('sedan')) {
      transportationMultiplier = 1.0; // Base price
    } else if (familyCabType.includes('innova') || familyCabType.includes('suv')) {
      transportationMultiplier = 1.15; // 15% increase
    } else if (familyCabType.includes('tempo traveller')) {
      transportationMultiplier = 1.3; // 30% increase
    } else if (familyCabType.includes('mini bus')) {
      transportationMultiplier = 1.5; // 50% increase
    }

    const finalPrice = totalPrice * transportationMultiplier;
    return Math.round(Math.max(0, finalPrice)); // Ensure non-negative result
  };

  // Calculate package costs for all family types using Quote Mapping data
  const calculatePackageCosts = async () => {
    if (!selectedBaselineQuote) {
      alert('Please select a customer\'s baseline quote first');
      return;
    }

    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
    if (!baselineQuote) {
      alert('Selected quote not found');
      return;
    }

    setIsCalculatingPackageCosts(true);

    try {
      console.log('Fetching quote mapping data for package cost calculation...');

      // Fetch quote mapping data
      const quoteMappingData = await fetchQuoteMappingData(selectedBaselineQuote);
      if (!quoteMappingData) {
        alert('Quote mapping data not found. Please ensure the quote has been mapped in the Quote Mapping tab first.');
        return;
      }

      // Fetch baseline quote data
      const baselineQuoteData = await fetchBaselineQuoteData(selectedBaselineQuote);
      if (!baselineQuoteData) {
        alert('Could not fetch baseline quote data');
        return;
      }

      console.log('Calculating package costs for all family types...');

      // Calculate package costs for each family type
      const updatedFamilyTypes = familyTypes.map(familyType => {
        console.log(`Calculating package cost for ${familyType.family_type || familyType.name}...`);

        const packageBreakdown = calculateFamilyTypePackageCost(
          familyType,
          baselineQuoteData,
          quoteMappingData
        );

        console.log(`Package cost for ${familyType.name}:`, packageBreakdown);

        return {
          ...familyType,
          packageCost: packageBreakdown.totalPackageCost,
          packageBreakdown
        };
      });

      setFamilyTypes(updatedFamilyTypes);
      setShowPackageCalculation(true);

      const calculatedCount = updatedFamilyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length;
      console.log(`Successfully calculated package costs for ${calculatedCount} family types`);

    } catch (error) {
      console.error('Error calculating package costs:', error);
      alert('Error calculating package costs. Please try again.');
    } finally {
      setIsCalculatingPackageCosts(false);
    }
  };

  // Enhanced Package Cost Calculation following Quote Generator workflow
  const calculateEnhancedPackageCosts = async () => {
    if (!selectedBaselineQuote) {
      alert('Please select a customer\'s baseline quote first');
      return;
    }

    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
    if (!baselineQuote) {
      alert('Selected quote not found');
      return;
    }

    setIsCalculatingPackageCosts(true);
    try {
      console.log('🔍 Starting Enhanced Package Cost Calculation following Quote Generator workflow');
      console.log('📊 Baseline Quote:', baselineQuote.customer_name, '-', baselineQuote.destination);

      // Phase 1: Family types already loaded from database
      console.log('✅ Phase 1: Family types loaded from database:', familyTypes.length);

      // Phase 2: Fetch Quote Mapping data
      const quoteMappingData = await fetchQuoteMappingData(selectedBaselineQuote);
      if (!quoteMappingData) {
        alert('Quote mapping data not found. Please create a quote mapping first in the Quote Mapping tab.');
        setIsCalculatingPackageCosts(false);
        return;
      }
      console.log('✅ Phase 2: Quote mapping data found');

      // Phase 3: Fetch complete baseline quote data with hotel rows
      const completeQuoteData = await fetchCompleteQuoteData(selectedBaselineQuote);
      if (!completeQuoteData) {
        alert('Complete quote data not found. Please ensure the quote has hotel details.');
        setIsCalculatingPackageCosts(false);
        return;
      }
      console.log('✅ Phase 3: Complete quote data with hotel rows found');

      // Phase 4: Calculate package costs for all family types following Quote Generator logic
      const updatedFamilyTypes = await Promise.all(
        familyTypes.map(async (familyType) => {
          console.log(`\n=== Enhanced Package Calculation: ${familyType.family_type || familyType.name} ===`);

          // Convert to database format for enhanced calculator
          const familyTypeDB = {
            family_id: familyType.family_id || familyType.id,
            family_type: familyType.family_type || familyType.name,
            no_of_adults: familyType.no_of_adults || familyType.adults,
            no_of_infants: familyType.no_of_infants || familyType.infants,
            no_of_child: familyType.no_of_child || 0, // Children ≤5 (free)
            no_of_children: familyType.no_of_children || familyType.children, // Children 6-12 (charged)
            family_count: familyType.family_count || familyType.totalCount,
            cab_type: familyType.cab_type || familyType.cabType,
            cab_capacity: familyType.cab_capacity || 4,
            rooms_need: familyType.rooms_need || familyType.rooms
          };

          try {
            const packageCalculation = await calculateEnhancedFamilyTypePackage(
              familyTypeDB,
              completeQuoteData,
              quoteMappingData,
              completeQuoteData.hotelRows
            );

            console.log(`✅ Enhanced package cost for ${familyType.name}:`, packageCalculation.finalCalculation.grandTotal);
            console.log('📊 Room calculation:', packageCalculation.roomCalculation);
            console.log('🏨 Hotel costs:', packageCalculation.hotelCosts);
            console.log('🚗 Vehicle costs:', packageCalculation.vehicleCosts);
            console.log('💰 Final calculation:', packageCalculation.finalCalculation);

            return {
              ...familyType,
              packageCost: packageCalculation.finalCalculation.grandTotal,
              enhancedPackageBreakdown: packageCalculation
            };
          } catch (error) {
            console.error(`❌ Error calculating for ${familyType.name}:`, error);
            return familyType; // Return original if calculation fails
          }
        })
      );

      setFamilyTypes(updatedFamilyTypes);
      setShowPackageCalculation(true);

      // Show success message
      const calculatedCount = updatedFamilyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length;
      console.log(`\n=== ENHANCED PACKAGE CALCULATION SUMMARY ===`);
      console.log(`✅ Successfully calculated enhanced package costs for ${calculatedCount} family types`);
      console.log('📊 Following Quote Generator workflow with actual hotel rates and Quote Mapping data');
      console.log('🎯 Baseline Quote:', {
        customer: baselineQuote.customer_name,
        total: baselineQuote.total_cost,
        destination: baselineQuote.destination,
        family_type: baselineQuote.family_type
      });

    } catch (error) {
      console.error('❌ Error in enhanced package cost calculation:', error);
      alert('Error calculating enhanced package costs. Please try again.');
    } finally {
      setIsCalculatingPackageCosts(false);
    }
  };

  // Calculate prices for all family types using enhanced logic
  const calculateAllPrices = async () => {
    if (!selectedBaselineQuote) {
      alert('Please select a customer\'s baseline quote first');
      return;
    }

    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
    if (!baselineQuote) {
      alert('Selected quote not found');
      return;
    }

    try {
      console.log('Fetching complete quote data for:', baselineQuote.customer_name);
      
      // Fetch complete quote data including hotel rows and costs
      const completeQuoteData = await fetchCompleteQuoteData(selectedBaselineQuote);
      
      if (!completeQuoteData) {
        alert('Could not fetch complete quote data. Using simplified calculation.');
        // Fallback to simple calculation
        const updatedFamilyTypes = familyTypes.map(familyType => {
          const calculatedPrice = calculateFamilyTypePrice(familyType, baselineQuote);
          return {
            ...familyType,
            calculatedPrice
          };
        });
        setFamilyTypes(updatedFamilyTypes);
        setShowPriceCalculation(true);
        return;
      }

      console.log('Using enhanced calculation with complete quote data');

      // Calculate prices using the enhanced calculator
      const updatedFamilyTypes = familyTypes.map(familyType => {
        console.log(`\n=== Calculating for ${familyType.family_type} ===`);
        console.log('Family Data:', {
          adults: familyType.no_of_adults || familyType.adults,
          children: familyType.no_of_children || familyType.children,
          infants: familyType.no_of_infants || familyType.infants,
          cabType: familyType.cab_type || familyType.cabType,
          cabCapacity: familyType.cab_capacity,
          roomsNeed: familyType.rooms_need || familyType.rooms
        });
        
        const calculation = calculateFamilyTypePriceDetailed(familyType, completeQuoteData);
        console.log(`Enhanced price for ${familyType.name}:`, calculation);
        console.log('Breakdown:', calculation.breakdown);
        
        return {
          ...familyType,
          calculatedPrice: calculation.totalPrice,
          priceBreakdown: calculation.breakdown
        };
      });

      setFamilyTypes(updatedFamilyTypes);
      setShowPriceCalculation(true);

      // Show success message with baseline quote details
      const calculatedCount = updatedFamilyTypes.filter(ft => ft.calculatedPrice && ft.calculatedPrice > 0).length;
      console.log(`\n=== CALCULATION SUMMARY ===`);
      console.log(`Successfully calculated enhanced prices for ${calculatedCount} family types based on ${baselineQuote.customer_name}'s quote`);
      console.log('Baseline Quote:', {
        customer: baselineQuote.customer_name,
        total: baselineQuote.total_cost,
        adults: (baselineQuote.no_of_persons || 0) + (baselineQuote.extra_adults || 0),
        children: baselineQuote.children,
        infants: baselineQuote.infants
      });
      
    } catch (error) {
      console.error('Error in enhanced calculation:', error);
      alert('Error calculating prices. Please try again.');
    }
  };

  const filteredFamilyTypes = useMemo(() => {
    return familyTypes.filter(type => {
      const matchesSearch = type.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          type.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          type.id.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesMaxMembers = filters.maxMembers === 'all' || type.totalCount <= parseInt(filters.maxMembers);
      const matchesVehicleType = filters.vehicleType === 'all' || 
                                 (type.cabType && type.cabType.includes(filters.vehicleType)) ||
                                 (type.cab_type && type.cab_type.includes(filters.vehicleType));
      const matchesInfants = !filters.hasInfants || type.infants > 0;
      const matchesGrandparents = !filters.hasGrandparents || type.grandparents > 0;

      return matchesSearch && matchesMaxMembers && matchesVehicleType && matchesInfants && matchesGrandparents;
    });
  }, [searchQuery, filters, familyTypes]);

  const handleAddNewFamily = () => {
    const totalCount = newFamilyType.adults + newFamilyType.infants + 
                      newFamilyType.children + newFamilyType.teenagers + 
                      newFamilyType.grandparents;
    
    const newFamily: FamilyType = {
      id: `FT-${Math.random().toString(36).substr(2, 9)}`,
      name: "Custom Family",
      description: `${newFamilyType.adults} Adults${newFamilyType.children ? ` + ${newFamilyType.children} Children` : ''}${newFamilyType.infants ? ` + ${newFamilyType.infants} Infants` : ''}${newFamilyType.grandparents ? ` + ${newFamilyType.grandparents} Grandparents` : ''}`,
      ...newFamilyType,
      totalCount
    };
    
    setFamilyTypes([...familyTypes, newFamily]);
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className="space-y-6">
      {/* Price Calculation Section */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 bg-blue-50">
          <div className="flex items-center gap-3">
            <Calculator className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-800">Family Type Price Calculator</h3>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Calculate package prices for all family types based on an existing quote
          </p>
        </div>
        
        <div className="p-6">
          {/* Enhanced Debug Information */}
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex justify-between items-start mb-2">
              <h4 className="text-sm font-medium text-yellow-800">Family Types Status:</h4>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    console.log('🧪 Database test triggered');
                    testDatabaseConnection();
                  }}
                  className="px-2 py-1 text-xs bg-blue-200 hover:bg-blue-300 text-blue-800 rounded"
                >
                  Test DB
                </button>
                <button
                  onClick={() => {
                    console.log('🔄 Manual refresh triggered');
                    fetchFamilyTypes();
                  }}
                  disabled={isLoadingFamilyTypes}
                  className="px-2 py-1 text-xs bg-yellow-200 hover:bg-yellow-300 text-yellow-800 rounded disabled:opacity-50"
                >
                  {isLoadingFamilyTypes ? 'Refreshing...' : 'Refresh Family Types'}
                </button>
                <button
                  onClick={() => {
                    console.log('🔄 Force complete refresh triggered');
                    setFamilyTypes([]); // Clear current data
                    setIsLoadingFamilyTypes(true);
                    setTimeout(() => {
                      fetchFamilyTypes();
                    }, 100);
                  }}
                  className="px-2 py-1 text-xs bg-red-200 hover:bg-red-300 text-red-800 rounded"
                >
                  Force Refresh
                </button>
              </div>
            </div>
            <div className="text-xs text-yellow-700 space-y-1">
              <p>Family Types Loaded: <strong>{familyTypes.length}</strong> ({isLoadingFamilyTypes ? 'Loading...' : 'Ready'})</p>
              <p>Family Types Source: <strong>{familyTypes.length === 3 && familyTypes[0]?.id === 'BB-1' ? '⚠️ Using Default Fallback' : '✅ From CRM Database'}</strong></p>
              <p>CRM Database: <strong>{crmSupabase ? '✅ Connected' : '❌ Not Available'}</strong> (Family Types)</p>
              <p>Quote Database: <strong>{quoteSupabase ? '✅ Connected' : '❌ Not Available'}</strong> (Quotes)</p>
              <p>CRM URL: <span className="font-mono text-xs">...{crmConfig.url.substring(20, 35)}...</span></p>
              <p>Quote URL: <span className="font-mono text-xs">...lkqbrlrmrsnbtkoryazq...</span></p>
              <p>Baseline Quotes Loaded: <strong>{baselineQuotes.length}</strong> ({baselineQuotes.filter(q => q.is_draft).length} drafts, {baselineQuotes.filter(q => !q.is_draft).length} final)</p>
              <p>Include Drafts: {includeDrafts ? 'Yes' : 'No'}</p>
              <p>Loading Quotes: {isLoadingQuotes ? 'Yes' : 'No'}</p>
              <p>Selected Quote: {selectedBaselineQuote || 'None'}</p>
              <p>Calculated Prices: {familyTypes.filter(ft => ft.calculatedPrice && ft.calculatedPrice > 0).length} families</p>
              <p>Package Costs: {familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length} families</p>
              {familyTypes.length > 0 && (
                <p>Cab Capacity: {familyTypes.filter(ft => ft.cabValidation?.isValid).length} adequate, {familyTypes.filter(ft => ft.cabValidation && !ft.cabValidation.isValid).length} undersized</p>
              )}
              {lastUpdated && (
                <p>Last Updated: {lastUpdated.toLocaleTimeString()}</p>
              )}
            </div>
            {familyTypes.length > 0 && (
              <details className="mt-2">
                <summary className="text-xs font-medium text-yellow-800 cursor-pointer">Show All Family Types Data</summary>
                <div className="mt-1 text-xs text-yellow-600 max-h-40 overflow-y-auto">
                  {familyTypes.map((ft, index) => (
                    <div key={ft.id} className="border-b border-yellow-200 py-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <strong>{index + 1}. {ft.family_type || ft.name}</strong>
                          <div className="ml-2 text-xs">
                            {ft.no_of_adults || ft.adults}A, {ft.no_of_children || ft.no_of_child || ft.children}C, {ft.no_of_infants || ft.infants}I
                            | Total: {ft.family_count || ft.totalCount}
                            | Cab: {ft.cab_type || ft.cabType} ({ft.cab_capacity} seats)
                            | Rooms: {ft.rooms_need || ft.rooms}
                          </div>
                        </div>
                        <div className="text-right">
                          {ft.cabValidation && (
                            <span className={`text-xs px-1 rounded ${ft.cabValidation.isValid ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                              {ft.cabValidation.isValid ? '✓' : '⚠'}
                            </span>
                          )}
                          {ft.packageCost && <div className="text-green-600 font-medium">₹{ft.packageCost.toLocaleString()}</div>}
                          {!ft.packageCost && ft.calculatedPrice && <div className="text-blue-600">₹{ft.calculatedPrice.toLocaleString()}</div>}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </details>
            )}
          </div>

          {/* No Quotes Available Message */}
          {baselineQuotes.length === 0 && !isLoadingQuotes && (
            <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-md">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-orange-800">No Baseline Quotes Available</h3>
                  <div className="mt-2 text-sm text-orange-700">
                    <p className="mb-2">To calculate family type prices, you need at least one existing saved quote to use as a baseline.</p>
                    <p className="font-medium">Steps to get started:</p>
                    <ol className="mt-1 ml-4 list-decimal space-y-1">
                      <li>Go to the <strong>Quote Generator</strong> tab</li>
                      <li>Create a complete quote for any customer (e.g., "Tropical Paradise in Andaman" for a couple)</li>
                      <li>Make sure to <strong>save the quote</strong> to the database (not as draft)</li>
                      <li>Return to this Family Type tab</li>
                      <li>Your saved quote will appear in the dropdown above</li>
                      <li>Select it and click "Calculate Prices" to get prices for all family types</li>
                    </ol>
                    <div className="mt-3 p-2 bg-orange-100 rounded text-xs">
                      <strong>Example:</strong> If you create a quote for ₹43,630 for "2 Adults + 1 Child" going to Andaman, 
                      the system will automatically calculate prices for all other family types like "4 Adults + 2 Children", "1 Adult + 2 Infants", etc.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Baseline Quote
              </label>
              
              {/* Include Drafts Toggle */}
              <div className="mb-3">
                <label className="flex items-center text-sm text-gray-600">
                  <input
                    type="checkbox"
                    checked={includeDrafts}
                    onChange={(e) => {
                      setIncludeDrafts(e.target.checked);
                      setSelectedBaselineQuote(''); // Reset selection when toggle changes
                    }}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  Include draft quotes
                </label>
              </div>
              
              <select
                value={selectedBaselineQuote}
                onChange={(e) => setSelectedBaselineQuote(e.target.value)}
                className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoadingQuotes}
              >
                <option value="">Choose a customer's baseline quote...</option>
                {baselineQuotes.map(quote => (
                  <option key={quote.id} value={quote.id}>
                    {quote.customer_name} - {quote.destination} ({quote.family_type}) - {formatPrice(quote.total_cost)} {quote.is_draft ? '(Draft)' : '(Final)'}
                  </option>
                ))}
              </select>
              
              {isLoadingQuotes && (
                <p className="text-sm text-gray-500 mt-1">Loading quotes...</p>
              )}
              
              {/* Display selected baseline quote details */}
              {selectedBaselineQuote && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  {(() => {
                    const selectedQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    if (!selectedQuote) return null;
                    
                    return (
                      <div className="text-sm">
                        <p className="font-medium text-blue-800">Selected Baseline Quote:</p>
                        <div className="mt-1 space-y-1 text-blue-700">
                          <p><span className="font-medium">Customer:</span> {selectedQuote.customer_name}</p>
                          <p><span className="font-medium">Destination:</span> {selectedQuote.destination}</p>
                          <p><span className="font-medium">Family Type:</span> {selectedQuote.family_type}</p>
                          <p><span className="font-medium">Total Cost:</span> {formatPrice(selectedQuote.total_cost)}</p>
                          <p><span className="font-medium">Composition:</span> {(selectedQuote.no_of_persons + selectedQuote.extra_adults)} Adults, {selectedQuote.children} Children, {selectedQuote.infants} Infants</p>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>
            
            <div className="flex items-end space-y-3">
              <div className="w-full space-y-3">
                <button
                  onClick={calculateAllPrices}
                  disabled={!selectedBaselineQuote}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Calculate Prices for All Family Types
                </button>

                <button
                  onClick={calculateEnhancedPackageCosts}
                  disabled={!selectedBaselineQuote || isCalculatingPackageCosts}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                >
                  <Package className="h-4 w-4" />
                  {isCalculatingPackageCosts ? 'Calculating Enhanced Package Costs...' : 'Calculate Enhanced Package Costs (Quote Generator Workflow)'}
                </button>

                <button
                  onClick={calculatePackageCosts}
                  disabled={!selectedBaselineQuote || isCalculatingPackageCosts}
                  className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                >
                  <Package className="h-4 w-4" />
                  {isCalculatingPackageCosts ? 'Calculating Package Costs...' : 'Calculate Package Costs (Legacy Method)'}
                </button>

                <div className="text-xs text-gray-500 text-center">
                  Enhanced calculation follows Quote Generator workflow with actual hotel rates
                </div>
              </div>
            </div>
          </div>

          {showPackageCalculation && selectedBaselineQuote && (
            <div className="mt-6 space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center gap-2 text-green-800">
                  <Package className="h-5 w-5" />
                  <span className="font-medium">Package cost calculation completed!</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  {(() => {
                    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    const calculatedCount = familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length;
                    return `Package costs have been calculated for ${calculatedCount} family types using Quote Mapping data from ${baselineQuote?.customer_name || 'selected customer'}'s quote for ${baselineQuote?.destination || 'destination'} (${formatPrice(baselineQuote?.total_cost || 0)}). Scroll down to see detailed package breakdowns.`;
                  })()}
                </p>
              </div>
            </div>
          )}

          {showPriceCalculation && selectedBaselineQuote && (
            <div className="mt-6 space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center gap-2 text-blue-800">
                  <DollarSign className="h-5 w-5" />
                  <span className="font-medium">Basic price calculation completed!</span>
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  {(() => {
                    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    const calculatedCount = familyTypes.filter(ft => ft.calculatedPrice && ft.calculatedPrice > 0).length;
                    return `Basic prices have been calculated for ${calculatedCount} family types based on ${baselineQuote?.customer_name || 'selected customer'}'s quote for ${baselineQuote?.destination || 'destination'} (${formatPrice(baselineQuote?.total_cost || 0)}). For more accurate pricing, use the Package Cost calculation above.`;
                  })()}
                </p>
              </div>

              {/* Pricing Insights */}
              {(() => {
                const insights = calculatePricingInsights(familyTypes);
                if (!insights) return null;
                
                return (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-center gap-2 text-blue-800 mb-3">
                      <BarChart3 className="h-5 w-5" />
                      <span className="font-medium">Pricing Insights</span>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-blue-600 font-medium">Total Family Types</p>
                        <p className="text-blue-800">{insights.totalFamilyTypes}</p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Price Range</p>
                        <p className="text-blue-800">{formatPrice(insights.minPrice)} - {formatPrice(insights.maxPrice)}</p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Average Price</p>
                        <p className="text-blue-800">{formatPrice(insights.avgPrice)}</p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Most/Least Expensive</p>
                        <p className="text-blue-800 text-xs">{insights.maxPriceFamily} / {insights.minPriceFamily}</p>
                      </div>
                    </div>
                  </div>
                );
              })()}

              {/* Export Button */}
              <div className="flex justify-end">
                <button
                  onClick={() => {
                    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    exportFamilyTypePricing(familyTypes, baselineQuote || null);
                  }}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Pricing Data (CSV)
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Family Types Summary */}
      {familyTypes.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 bg-green-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">All Family Types from Database</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Complete list of {familyTypes.length} family types with pricing information
                  </p>
                </div>
              </div>
              <button
                onClick={() => {
                  console.log('Manual refresh triggered');
                  fetchFamilyTypes();
                }}
                disabled={isLoadingFamilyTypes}
                className="inline-flex items-center px-3 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-white hover:bg-green-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg className={`w-4 h-4 mr-2 ${isLoadingFamilyTypes ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {isLoadingFamilyTypes ? 'Refreshing...' : 'Refresh Data'}
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="text-sm font-medium text-blue-800">Total Family Types</div>
                <div className="text-2xl font-bold text-blue-900">{familyTypes.length}</div>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="text-sm font-medium text-green-800">With Package Prices</div>
                <div className="text-2xl font-bold text-green-900">{familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length}</div>
              </div>
              <div className="bg-yellow-50 p-3 rounded-lg">
                <div className="text-sm font-medium text-yellow-800">With Basic Prices</div>
                <div className="text-2xl font-bold text-yellow-900">{familyTypes.filter(ft => ft.calculatedPrice && ft.calculatedPrice > 0).length}</div>
              </div>
              <div className="bg-red-50 p-3 rounded-lg">
                <div className="text-sm font-medium text-red-800">Cab Issues</div>
                <div className="text-2xl font-bold text-red-900">{familyTypes.filter(ft => ft.cabValidation && !ft.cabValidation.isValid).length}</div>
              </div>
            </div>

            {/* Compact Family Types Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Composition</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Package Price</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Price</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {familyTypes.map((type, index) => (
                    <tr key={type.id} className={`hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{type.family_type || type.name}</div>
                        <div className="text-xs text-gray-500">{type.family_id || type.id}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <div>{type.no_of_adults || type.adults}A</div>
                        <div className="text-xs">
                          {(type.no_of_children || type.no_of_child || type.children) > 0 && `${type.no_of_children || type.no_of_child || type.children}C `}
                          {(type.no_of_infants || type.infants) > 0 && `${type.no_of_infants || type.infants}I`}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <div>{type.cab_type || type.cabType}</div>
                        <div className="text-xs flex items-center gap-1">
                          <span>{type.cab_capacity} seats</span>
                          {type.cabValidation && (
                            <span className={`px-1 rounded ${type.cabValidation.isValid ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                              {type.cabValidation.isValid ? '✓' : '⚠'}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {type.rooms_need || type.rooms || 1}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                        {type.packageCost ? (
                          <div className="font-medium text-green-900">{formatPrice(type.packageCost)}</div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                        {type.calculatedPrice ? (
                          <div className="font-medium text-blue-900">{formatPrice(type.calculatedPrice)}</div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                        <div className="flex flex-col gap-1">
                          {type.packageCost && <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Package ✓</span>}
                          {!type.packageCost && type.calculatedPrice && <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Basic ✓</span>}
                          {!type.packageCost && !type.calculatedPrice && <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">No Price</span>}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Search family types..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            
            <div className="flex gap-2">
                <select
                  className="block w-full md:w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  value={filters.maxMembers}
                  onChange={(e) => setFilters(prev => ({ ...prev, maxMembers: e.target.value }))}
                >
                  <option value="all">All Sizes</option>
                  <option value="3">Up to 3 members</option>
                  <option value="4">Up to 4 members</option>
                  <option value="5">Up to 5 members</option>
                  <option value="6">Up to 6 members</option>
                </select>
                <select
                  className="block w-full md:w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  value={filters.vehicleType}
                  onChange={(e) => setFilters(prev => ({ ...prev, vehicleType: e.target.value }))}
                >
                  <option value="all">All Vehicles</option>
                  <option value="Sedan">Sedan</option>
                  <option value="SUV">SUV</option>
                  <option value="Innova">Innova</option>
                </select>
                <button
                  className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium ${
                    filters.hasInfants
                      ? 'bg-blue-50 border-blue-300 text-blue-800'
                      : 'bg-white border-gray-300 text-gray-700'
                  }`}
                  onClick={() => setFilters(prev => ({ ...prev, hasInfants: !prev.hasInfants }))}
                >
                  <Baby className="h-4 w-4 mr-2" />
                  With Infants
                </button>
                <button
                  className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium ${
                    filters.hasGrandparents
                      ? 'bg-blue-50 border-blue-300 text-blue-800'
                      : 'bg-white border-gray-300 text-gray-700'
                  }`}
                  onClick={() => setFilters(prev => ({ ...prev, hasGrandparents: !prev.hasGrandparents }))}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  With Grandparents
                </button>
              </div>
            </div>
          </div>

        {/* Family Type Cards */}
        <div className="p-6">
          {isLoadingFamilyTypes ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Loading family types...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredFamilyTypes.map((type) => (
                <div
                  key={type.id}
                  className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedFamily === type.id
                      ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedFamily(type.id)}
                >
                  <div className="absolute top-4 right-4">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {type.totalCount} members
                    </span>
                  </div>
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{type.name}</h3>
                    <p className="text-sm text-gray-500">{type.id}</p>
                    <p className="mt-1 text-sm text-gray-600">{type.description}</p>
                    
                    {/* Display package cost (priority) */}
                    {type.packageCost && (
                      <div className="mt-2 space-y-2">
                        <div className="p-2 bg-green-50 border border-green-200 rounded-md">
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium text-green-800">
                              Package Cost: {formatPrice(type.packageCost)}
                            </span>
                          </div>
                          <div className="text-xs text-green-600 mt-1">
                            Based on Quote Mapping data
                          </div>
                        </div>

                        {/* Package breakdown */}
                        {type.packageBreakdown && (
                          <div className="p-2 bg-green-50 border border-green-200 rounded-md">
                            <div className="flex items-center gap-2 mb-2">
                              <TrendingUp className="h-4 w-4 text-green-600" />
                              <span className="text-xs font-medium text-green-800">Package Breakdown</span>
                            </div>
                            <div className="space-y-1 text-xs text-green-700">
                              <div className="flex justify-between">
                                <span>Room Cost:</span>
                                <span>{formatPrice(type.packageBreakdown.roomCost)}</span>
                              </div>
                              {type.packageBreakdown.extraAdultCost > 0 && (
                                <div className="flex justify-between">
                                  <span>Extra Adults:</span>
                                  <span>{formatPrice(type.packageBreakdown.extraAdultCost)}</span>
                                </div>
                              )}
                              {type.packageBreakdown.childrenCost > 0 && (
                                <div className="flex justify-between">
                                  <span>Children (6-12):</span>
                                  <span>{formatPrice(type.packageBreakdown.childrenCost)}</span>
                                </div>
                              )}
                              <div className="flex justify-between">
                                <span>Vehicle Cost:</span>
                                <span>{formatPrice(type.packageBreakdown.vehicleCost)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Additional Costs:</span>
                                <span>{formatPrice(type.packageBreakdown.additionalCosts)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Commission:</span>
                                <span>{formatPrice(type.packageBreakdown.commission)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>GST:</span>
                                <span>{formatPrice(type.packageBreakdown.gst)}</span>
                              </div>
                              <div className="border-t border-green-300 mt-1 pt-1 text-xs">
                                <div className="flex justify-between">
                                  <span>Rooms: {type.packageBreakdown.roomsNeeded}</span>
                                  <span>Extra Adults: {type.packageBreakdown.extraAdultsCharged}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Children (6-12): {type.packageBreakdown.childrenCharged}</span>
                                  <span>Infants Free: {type.packageBreakdown.infantsIncluded}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Display basic calculated price (fallback) */}
                    {!type.packageCost && type.calculatedPrice && (
                      <div className="mt-2 space-y-2">
                        <div className="p-2 bg-blue-50 border border-blue-200 rounded-md">
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-800">
                              Estimated Price: {formatPrice(type.calculatedPrice)}
                            </span>
                          </div>
                          <div className="text-xs text-blue-600 mt-1">
                            Basic calculation - Use Package Cost for accuracy
                          </div>
                        </div>
                        
                        {/* Price breakdown */}
                        {type.priceBreakdown && (
                          <div className="p-2 bg-blue-50 border border-blue-200 rounded-md">
                            <div className="flex items-center gap-2 mb-2">
                              <Info className="h-4 w-4 text-blue-600" />
                              <span className="text-xs font-medium text-blue-800">Price Breakdown</span>
                            </div>
                            <div className="space-y-1 text-xs text-blue-700">
                              <div className="flex justify-between">
                                <span>Hotel Cost:</span>
                                <span>{formatPrice(type.priceBreakdown.hotelCost)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Other Costs:</span>
                                <span>{formatPrice(type.priceBreakdown.otherCosts)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Commission:</span>
                                <span>{formatPrice(type.priceBreakdown.commission)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>GST:</span>
                                <span>{formatPrice(type.priceBreakdown.gst)}</span>
                              </div>
                              <div className="border-t border-blue-300 mt-1 pt-1 text-xs">
                                <div className="flex justify-between">
                                  <span>Rooms: {type.priceBreakdown.roomsNeeded}</span>
                                  <span>Extra Adults: {type.priceBreakdown.extraAdults}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Children (6-12): {type.priceBreakdown.childrenCharged}</span>
                                  <span>Free (≤5): {type.priceBreakdown.infantsFree}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="w-4 h-4 mr-2 text-gray-400" />
                        <span>{type.adults} Adults</span>
                      </div>
                      {type.infants > 0 && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Baby className="w-4 h-4 mr-2 text-gray-400" />
                          <span>{type.infants} Infant{type.infants > 1 ? 's' : ''}</span>
                        </div>
                      )}
                      {type.children > 0 && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Child className="w-4 h-4 mr-2 text-gray-400" />
                          <span>{type.children} Child{type.children > 1 ? 'ren' : ''}</span>
                        </div>
                      )}
                      {type.grandparents > 0 && (
                        <div className="flex items-center text-sm text-gray-600">
                          <UserPlus className="w-4 h-4 mr-2 text-gray-400" />
                          <span>{type.grandparents} Grandparent{type.grandparents > 1 ? 's' : ''}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 gap-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center text-gray-600">
                          <Car className="w-4 h-4 mr-2 text-gray-400" />
                          <span>{type.cabType || type.cab_type || 'Standard'}</span>
                        </div>
                        {type.cab_capacity && (
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-gray-500">{type.cab_capacity} seats</span>
                            {type.cabValidation && (
                              <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                                type.cabValidation.isValid
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {type.cabValidation.isValid ? '✓' : '⚠'}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Home className="w-4 h-4 mr-2 text-gray-400" />
                        <span>{type.rooms || type.rooms_need || 1} Room{((type.rooms || 0) > 1 || (type.rooms_need || 0) > 1) ? 's' : ''}</span>
                      </div>
                      {type.cabValidation && !type.cabValidation.isValid && (
                        <div className="text-xs text-red-600 bg-red-50 p-2 rounded border border-red-200">
                          <div className="flex items-center gap-1">
                            <span>⚠</span>
                            <span>Cab capacity insufficient for {type.totalCount || type.family_count} members</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
                     )}
          </div>

          {/* Package Cost Table View */}
          {showPackageCalculation && familyTypes.some(ft => ft.packageCost) && (
            <div className="p-6 border-t border-gray-200">
              <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <Package className="h-5 w-5 text-green-600" />
                Package Cost Comparison Table
              </h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-green-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Composition</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room Cost</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle Cost</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Additional</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Package</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Per Person</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {familyTypes
                      .filter(ft => ft.packageCost)
                      .sort((a, b) => (a.packageCost || 0) - (b.packageCost || 0))
                      .map((type) => (
                      <tr key={type.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{type.name}</div>
                          <div className="text-sm text-gray-500">{type.id}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.adults || type.no_of_adults}A
                          {(type.children || type.no_of_children || type.no_of_child) > 0 && `, ${(type.children || type.no_of_children || type.no_of_child)}C`}
                          {(type.infants || type.no_of_infants) > 0 && `, ${(type.infants || type.no_of_infants)}I`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.packageBreakdown?.roomsNeeded || type.rooms || type.rooms_need || 1}
                          {type.packageBreakdown && type.packageBreakdown.extraAdultsCharged > 0 && (
                            <span className="text-xs text-orange-600 ml-1">
                              (+{type.packageBreakdown.extraAdultsCharged} extra)
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.packageBreakdown ? formatPrice(type.packageBreakdown.roomCost + type.packageBreakdown.extraAdultCost + type.packageBreakdown.childrenCost) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.packageBreakdown ? formatPrice(type.packageBreakdown.vehicleCost) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.packageBreakdown ? formatPrice(type.packageBreakdown.additionalCosts + type.packageBreakdown.commission + type.packageBreakdown.gst) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-green-900">
                            {formatPrice(type.packageCost || 0)}
                          </div>
                          {type.packageBreakdown && type.packageBreakdown.childrenCharged > 0 && (
                            <div className="text-xs text-green-600">
                              {type.packageBreakdown.childrenCharged} children charged
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.totalCount > 0 ? formatPrice(Math.round((type.packageCost || 0) / type.totalCount)) : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Basic Pricing Table View */}
          {showPriceCalculation && familyTypes.some(ft => ft.calculatedPrice) && (
            <div className="p-6 border-t border-gray-200">
              <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-blue-600" />
                Basic Price Comparison Table
              </h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Composition</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hotel Cost</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Other Costs</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price per Person</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {familyTypes
                      .filter(ft => ft.calculatedPrice)
                      .sort((a, b) => (a.calculatedPrice || 0) - (b.calculatedPrice || 0))
                      .map((type) => (
                      <tr key={type.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{type.name}</div>
                          <div className="text-sm text-gray-500">{type.id}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.adults}A{type.children > 0 && `, ${type.children}C`}{type.infants > 0 && `, ${type.infants}I`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.priceBreakdown?.roomsNeeded || type.rooms || type.rooms_need || 1}
                          {type.priceBreakdown && type.priceBreakdown.extraAdults > 0 && (
                            <span className="text-xs text-orange-600 ml-1">
                              (+{type.priceBreakdown.extraAdults} extra)
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.priceBreakdown ? formatPrice(type.priceBreakdown.hotelCost) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.priceBreakdown ? formatPrice(type.priceBreakdown.otherCosts + type.priceBreakdown.commission + type.priceBreakdown.gst) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatPrice(type.calculatedPrice || 0)}
                          </div>
                          {type.priceBreakdown && type.priceBreakdown.childrenCharged > 0 && (
                            <div className="text-xs text-blue-600">
                              {type.priceBreakdown.childrenCharged} children charged
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {type.totalCount > 0 ? formatPrice(Math.round((type.calculatedPrice || 0) / type.totalCount)) : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t border-gray-200 flex justify-between items-center">
            <p className="text-sm text-gray-500">
              {filteredFamilyTypes.length} family types available
              {showPackageCalculation && ` • Package costs calculated for ${familyTypes.filter(t => t.packageCost).length} types`}
              {showPriceCalculation && !showPackageCalculation && ` • Basic prices calculated for ${familyTypes.filter(t => t.calculatedPrice).length} types`}
            </p>
          
          <div className="flex gap-2">
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                onClick={() => {
                  setSelectedFamily('');
                  setSearchQuery('');
                  setFilters({
                    maxMembers: 'all',
                    vehicleType: 'all',
                    hasInfants: false,
                    hasGrandparents: false
                  });
                }}
              >
                Reset
              </button>
              <button
                type="button"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                disabled={!selectedFamily}
              >
                Save Selection
              </button>
          </div>
            </div>
          </div>
        </div>
  );
}

export default FamilyTypeTab;