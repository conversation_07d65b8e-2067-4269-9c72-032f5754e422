import React, { useState, useEffect } from 'react';
import { Calculator, Save, Download, AlertCircle, CheckCircle, Plus, Minus, MapPin, Trash2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { getQuoteClient } from '../../lib/supabaseManager';

// Interfaces
interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  total_cost: number;
  no_of_persons: number;
  children: number;
  infants: number;
  extra_adults: number;
  is_draft: boolean;
}

interface HotelCostMapping {
  hotel_name: string;
  extra_adult_cost: number;
  children_cost: number; // 6-12 years
  infant_cost: number; // Usually 0
  grandparent_discount: number; // Percentage discount for grandparents
}

interface VehicleCostMapping {
  vehicle_type: string;
  pricing_type: 'multiplier' | 'actual_cost';
  base_cost: number;
  cost_multiplier: number;
  max_capacity: number;
  is_active: boolean;
}

interface QuoteMappingData {
  id?: string;
  quote_id: string;
  quote_name: string;
  customer_name: string;
  destination: string;
  hotel_mappings: HotelCostMapping[];
  vehicle_mappings: VehicleCostMapping[];
  additional_costs: {
    meal_cost_per_person: number;
    ferry_cost: number;
    activity_cost_per_person: number;
    guide_cost_per_day: number;
    parking_toll_multiplier: number;
  };
  created_at?: string;
  updated_at?: string;
}

const VEHICLE_TYPE_OPTIONS = [
  'Sedan/Dzire',
  'Innova/Crysta', 
  'Ertiga/XL6',
  'SUV/Scorpio',
  'Mahindra XUV',
  'Tempo Traveller (9 Seater)',
  'Tempo Traveller (12 Seater)',
  'Tempo Traveller (17 Seater)',
  'Mini Bus (18-25 Seater)',
  'Medium Bus (26-35 Seater)',
  'Large Bus (35+ Seater)',
  'Luxury Sedan',
  'Premium SUV'
];

const DEFAULT_VEHICLE_MAPPINGS = [
  { vehicle_type: 'Sedan/Dzire', pricing_type: 'multiplier' as const, base_cost: 0, cost_multiplier: 1.0, max_capacity: 4, is_active: true },
  { vehicle_type: 'Innova/Crysta', pricing_type: 'multiplier' as const, base_cost: 0, cost_multiplier: 1.2, max_capacity: 7, is_active: true },
  { vehicle_type: 'SUV/Scorpio', pricing_type: 'multiplier' as const, base_cost: 0, cost_multiplier: 1.25, max_capacity: 8, is_active: true }
];



function QuoteMappingTab() {
  const { user } = useAuth();
  const [supabase, setSupabase] = useState<any>(null);

  // Initialize Supabase client
  useEffect(() => {
    const initSupabase = async () => {
      const client = await getQuoteClient();
      setSupabase(client);
    };
    initSupabase();
  }, []);

  // State management
  const [baselineQuotes, setBaselineQuotes] = useState<BaselineQuote[]>([]);
  const [selectedQuoteId, setSelectedQuoteId] = useState('');
  const [selectedQuote, setSelectedQuote] = useState<BaselineQuote | null>(null);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Quote mapping data
  const [quoteMappingData, setQuoteMappingData] = useState<QuoteMappingData>({
    quote_id: '',
    quote_name: '',
    customer_name: '',
    destination: '',
    hotel_mappings: [],
    vehicle_mappings: DEFAULT_VEHICLE_MAPPINGS,
    additional_costs: {
      meal_cost_per_person: 0,
      ferry_cost: 0,
      activity_cost_per_person: 0,
      guide_cost_per_day: 0,
      parking_toll_multiplier: 1.0
    }
  });

  // Function to get detailed database information
  const getDatabaseInfo = async () => {
    if (!supabase) {
      return { success: false, message: 'Supabase client not available' };
    }

    try {
      // Get the current Supabase URL being used
      const supabaseUrl = supabase.supabaseUrl;
      console.log('[QuoteMapping] Current Supabase URL:', supabaseUrl);

      return {
        success: true,
        message: `Connected to: ${supabaseUrl}`,
        url: supabaseUrl
      };
    } catch (error) {
      return { success: false, message: `Failed to get database info: ${error instanceof Error ? error.message : 'Unknown error'}` };
    }
  };

  // Function to test database connection and table existence
  const testDatabaseConnection = async () => {
    if (!supabase) {
      return { success: false, message: 'Supabase client not available' };
    }

    try {
      console.log('[QuoteMapping] Testing database connection...');

      // Get database info first
      const dbInfo = await getDatabaseInfo();
      console.log('[QuoteMapping] Database info:', dbInfo);

      // Test authentication first
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      console.log('[QuoteMapping] Session check:', { sessionData, sessionError });

      if (sessionError) {
        return {
          success: false,
          message: `Authentication failed: ${sessionError.message}\nDatabase: ${dbInfo.url || 'Unknown'}`
        };
      }

      // Test basic connection with quotes table
      console.log('[QuoteMapping] Testing quotes table access...');
      const { data: quotesData, error: connectionError } = await supabase
        .from('quotes')
        .select('id')
        .limit(1);

      console.log('[QuoteMapping] Quotes table test:', { quotesData, connectionError });

      if (connectionError) {
        return {
          success: false,
          message: `Database connection failed: ${connectionError.message} (Code: ${connectionError.code})\nDatabase: ${dbInfo.url || 'Unknown'}`
        };
      }

      // Test quote_mappings table existence and access
      console.log('[QuoteMapping] Testing quote_mappings table access...');
      const { data: mappingsData, error: tableError } = await supabase
        .from('quote_mappings')
        .select('id')
        .limit(1);

      console.log('[QuoteMapping] Quote mappings table test:', { mappingsData, tableError });

      if (tableError) {
        if (tableError.code === '42P01') {
          return {
            success: false,
            message: `quote_mappings table does not exist in database: ${dbInfo.url || 'Unknown'}\n\nPlease create the table using the database-setup.sql script.`
          };
        } else if (tableError.code === '42501') {
          return {
            success: false,
            message: `Permission denied accessing quote_mappings table.\nDatabase: ${dbInfo.url || 'Unknown'}\n\nCheck RLS policies and authentication.`
          };
        } else {
          return {
            success: false,
            message: `Error accessing quote_mappings table: ${tableError.message} (Code: ${tableError.code})\nDatabase: ${dbInfo.url || 'Unknown'}`
          };
        }
      }

      return {
        success: true,
        message: `Database connection and table verified successfully!\nDatabase: ${dbInfo.url || 'Unknown'}\nQuotes found: ${quotesData?.length || 0}\nMappings found: ${mappingsData?.length || 0}`
      };
    } catch (error) {
      console.error('[QuoteMapping] Database test error:', error);
      const dbInfo = await getDatabaseInfo();
      return {
        success: false,
        message: `Database test failed: ${error instanceof Error ? error.message : 'Unknown error'}\nDatabase: ${dbInfo.url || 'Unknown'}`
      };
    }
  };

  // Function to refresh authentication and connection
  const refreshConnection = async () => {
    if (!supabase) {
      return { success: false, message: 'Supabase client not available' };
    }

    try {
      console.log('[QuoteMapping] Refreshing connection...');

      // Try to refresh the session
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

      if (refreshError) {
        console.error('[QuoteMapping] Session refresh failed:', refreshError);
        return { success: false, message: `Session refresh failed: ${refreshError.message}` };
      }

      if (refreshData.session) {
        console.log('[QuoteMapping] Session refreshed successfully');

        // Test table access after refresh
        const { error: testError } = await supabase
          .from('quote_mappings')
          .select('id')
          .limit(1);

        if (testError) {
          return { success: false, message: `Table access still failed after refresh: ${testError.message}` };
        }

        return { success: true, message: 'Connection refreshed successfully and table access verified' };
      } else {
        return { success: false, message: 'Session refresh did not return a valid session' };
      }
    } catch (error) {
      console.error('[QuoteMapping] Connection refresh error:', error);
      return { success: false, message: `Connection refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}` };
    }
  };

  // Function to check authentication status
  const checkAuthentication = async () => {
    if (!supabase) {
      return { authenticated: false, message: 'Supabase client not available' };
    }

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        return { authenticated: false, message: `Authentication error: ${sessionError.message}` };
      }

      if (!sessionData.session) {
        return { authenticated: false, message: 'No active session found' };
      }

      // Also test table access as part of auth check
      const { error: tableError } = await supabase
        .from('quote_mappings')
        .select('id')
        .limit(1);

      if (tableError) {
        return {
          authenticated: true,
          message: `Authentication OK but table access failed: ${tableError.message} (Code: ${tableError.code})`
        };
      }

      return { authenticated: true, message: 'Authentication verified and table access confirmed' };
    } catch (error) {
      return { authenticated: false, message: `Authentication check failed: ${error instanceof Error ? error.message : 'Unknown error'}` };
    }
  };

  // Function to ensure quote_mappings table exists and is accessible
  const ensureQuoteMappingsTableExists = async () => {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }

    try {
      console.log('[QuoteMapping] Checking table accessibility...');

      // First, try a simple select to test table access
      const { data, error: selectError } = await supabase
        .from('quote_mappings')
        .select('id')
        .limit(1);

      if (selectError) {
        console.error('[QuoteMapping] Table access error:', selectError);

        if (selectError.code === '42P01') {
          throw new Error('quote_mappings table does not exist in the connected database.');
        } else if (selectError.code === '42501') {
          throw new Error('Permission denied accessing quote_mappings table. Check authentication and RLS policies.');
        } else if (selectError.message.includes('JWT')) {
          throw new Error('Authentication token invalid. Please refresh the page and log in again.');
        } else {
          throw new Error(`Database access error: ${selectError.message} (Code: ${selectError.code})`);
        }
      }

      console.log('[QuoteMapping] Table access verified successfully');
      return true;

    } catch (error) {
      console.error('[QuoteMapping] Error checking table accessibility:', error);
      throw error; // Re-throw to be handled by calling function
    }
  };

  // Load baseline quotes
  useEffect(() => {
    fetchBaselineQuotes();
  }, []);

  // Also fetch quotes when Supabase client becomes available
  useEffect(() => {
    if (supabase && baselineQuotes.length === 0 && !isLoadingQuotes) {
      console.log('[QuoteMapping] Supabase client available, fetching quotes...');
      fetchBaselineQuotes();
    }
  }, [supabase]);

  const fetchBaselineQuotes = async () => {
    setIsLoadingQuotes(true);
    try {
      if (!supabase) {
        console.error('[QuoteMapping] Supabase client not available');
        setIsLoadingQuotes(false);
        return;
      }

      console.log('[QuoteMapping] Fetching baseline quotes...');

      // Add timeout to the query
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Query timeout after 10 seconds')), 10000);
      });

      const queryPromise = supabase
        .from('quotes')
        .select('id, package_name, customer_name, destination, family_type, total_cost, no_of_persons, children, infants, extra_adults, is_draft')
        .not('total_cost', 'is', null)
        .gt('total_cost', 0)
        .order('created_at', { ascending: false })
        .limit(50);

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      if (error) {
        console.error('[QuoteMapping] Error fetching baseline quotes:', error);
        alert(`Error loading quotes: ${error.message || 'Unknown error'}`);
        return;
      }

      console.log('[QuoteMapping] Successfully fetched quotes:', data?.length || 0);
      setBaselineQuotes(data || []);

    } catch (error: any) {
      console.error('[QuoteMapping] Exception fetching baseline quotes:', error);
      
      if (error.message?.includes('timeout')) {
        alert('Loading quotes is taking too long. Please check your internet connection and try again.');
      } else {
        alert(`Failed to load quotes: ${error.message || 'Unknown error'}`);
      }
      
      setBaselineQuotes([]);
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  const handleQuoteSelection = async (quoteId: string) => {
    setSelectedQuoteId(quoteId);
    const quote = baselineQuotes.find(q => q.id === quoteId);
    
    if (!quote) return;
    
    setSelectedQuote(quote);

    try {
      // Fetch hotel data for this quote
      const { data: hotelData, error: hotelError } = await supabase
        .from('hotel_rows')
        .select('hotel_name, extra_adult_cost, children_cost, infant_cost')
        .eq('quote_id', quoteId);

      if (hotelError) throw hotelError;

      // Fetch costs data for this quote
      const { data: costsData, error: costsError } = await supabase
        .from('costs')
        .select('transportation, cab_sightseeing, meals, ferry_cost, add_on_activity, guide_wages, parking_toll')
        .eq('quote_id', quoteId)
        .single();

      if (costsError && costsError.code !== 'PGRST116') { // PGRST116 = no rows found
        console.warn('[QuoteMapping] No costs data found for quote:', costsError);
      }

      // Create hotel mappings from existing data
      const hotelMappings: HotelCostMapping[] = (hotelData || []).map((hotel: any) => ({
        hotel_name: hotel.hotel_name || 'Unknown Hotel',
        extra_adult_cost: hotel.extra_adult_cost || 0,
        children_cost: hotel.children_cost || 0,
        infant_cost: hotel.infant_cost || 0,
        grandparent_discount: 10 // Default 10% discount
      }));

      // Auto-populate additional costs from quote data
      const autoAdditionalCosts = {
        meal_cost_per_person: 0, // Can be calculated if needed
        ferry_cost: costsData?.ferry_cost || 0,
        activity_cost_per_person: 0, // Can be calculated if needed  
        guide_cost_per_day: costsData?.guide_wages || 0,
        parking_toll_multiplier: 1.0
      };

      // Auto-populate vehicle base costs from cab sightseeing data
      const cabSightseeingCost = costsData?.cab_sightseeing || 0;
      const autoVehicleMappings = DEFAULT_VEHICLE_MAPPINGS.map(vehicle => ({
        ...vehicle,
        base_cost: cabSightseeingCost > 0 ? cabSightseeingCost : vehicle.base_cost
      }));

      // Check if mapping already exists
      let existingMapping = null;
      try {
        console.log('[QuoteMapping] Checking for existing mapping for quote:', quoteId);

        const { data, error: mappingError } = await supabase
          .from('quote_mappings')
          .select('*')
          .eq('quote_id', quoteId)
          .single();

        console.log('[QuoteMapping] Existing mapping query result:', { data, mappingError });

        if (mappingError) {
          if (mappingError.code === 'PGRST116') {
            // No rows found - this is normal for new quotes
            console.log('[QuoteMapping] No existing mapping found, will create new one');
          } else if (mappingError.code === '42P01') {
            // Table doesn't exist
            console.warn('[QuoteMapping] quote_mappings table does not exist');
            alert('Database table missing. Please contact administrator to set up the quote_mappings table.');
          } else if (mappingError.code === '42501') {
            // Permission denied
            console.error('[QuoteMapping] Permission denied accessing quote_mappings table');
            alert('Permission denied. Please check your authentication or contact administrator.');
          } else {
            console.warn('[QuoteMapping] Error fetching existing mapping:', mappingError);
            alert(`Database error: ${mappingError.message}. Please try again or contact administrator.`);
          }
        } else {
          existingMapping = data;
          console.log('[QuoteMapping] Found existing mapping:', existingMapping);
        }
      } catch (error) {
        console.error('[QuoteMapping] Failed to check for existing mapping:', error);
        alert('Failed to check for existing mapping. Please try again.');
      }

      if (existingMapping) {
        // Load existing mapping but update with fresh quote data
        setQuoteMappingData({
          id: existingMapping.id,
          quote_id: quoteId,
          quote_name: existingMapping.quote_name,
          customer_name: existingMapping.customer_name,
          destination: existingMapping.destination,
          hotel_mappings: hotelMappings, // Always use fresh hotel data
          vehicle_mappings: existingMapping.vehicle_mappings?.map((vm: any) => ({
            ...vm,
            // Update base_cost from quote if we have cab sightseeing cost
            base_cost: (cabSightseeingCost > 0 && vm.pricing_type === 'actual_cost') ? cabSightseeingCost : vm.base_cost
          })) || autoVehicleMappings,
          additional_costs: {
            ...existingMapping.additional_costs,
            // Update specific costs from quote data
            ferry_cost: costsData?.ferry_cost || existingMapping.additional_costs?.ferry_cost || 0,
            guide_cost_per_day: costsData?.guide_wages || existingMapping.additional_costs?.guide_cost_per_day || 0
          }
        });
      } else {
        // Create new mapping with auto-populated data
        setQuoteMappingData({
          quote_id: quoteId,
          quote_name: quote.package_name || 'Untitled Package',
          customer_name: quote.customer_name || 'Unknown Customer',
          destination: quote.destination || 'Unknown Destination',
          hotel_mappings: hotelMappings,
          vehicle_mappings: autoVehicleMappings,
          additional_costs: autoAdditionalCosts
        });
      }

      // Show success message about auto-populated data
      const autoDataMessage = [];
      if (hotelData && hotelData.length > 0) {
        autoDataMessage.push(`${hotelData.length} hotel(s) with extra adult costs`);
      }
      if (cabSightseeingCost > 0) {
        autoDataMessage.push(`cab sightseeing cost ₹${cabSightseeingCost.toLocaleString()}`);
      }
      if (costsData?.ferry_cost) {
        autoDataMessage.push(`ferry cost ₹${costsData.ferry_cost.toLocaleString()}`);
      }
      if (costsData?.guide_wages) {
        autoDataMessage.push(`guide cost ₹${costsData.guide_wages.toLocaleString()}`);
      }

      if (autoDataMessage.length > 0) {
        console.log(`[QuoteMapping] Auto-populated: ${autoDataMessage.join(', ')}`);
      }

    } catch (error) {
      console.error('[QuoteMapping] Error fetching quote data:', error);
      alert('Error loading quote data. Some fields may need to be filled manually.');
    }
  };

  const updateHotelMapping = (index: number, field: keyof HotelCostMapping, value: number | string) => {
    const updatedMappings = [...quoteMappingData.hotel_mappings];
    updatedMappings[index] = { ...updatedMappings[index], [field]: value };
    setQuoteMappingData({ ...quoteMappingData, hotel_mappings: updatedMappings });
  };

  const updateVehicleMapping = (index: number, field: keyof VehicleCostMapping, value: number | string | boolean) => {
    const updatedMappings = [...quoteMappingData.vehicle_mappings];
    updatedMappings[index] = { ...updatedMappings[index], [field]: value };
    setQuoteMappingData({ ...quoteMappingData, vehicle_mappings: updatedMappings });
  };

  const addVehicleMapping = () => {
    const newMapping: VehicleCostMapping = {
      vehicle_type: 'Sedan/Dzire',
      pricing_type: 'multiplier',
      base_cost: 0,
      cost_multiplier: 1.0,
      max_capacity: 4,
      is_active: true
    };
    setQuoteMappingData({
      ...quoteMappingData,
      vehicle_mappings: [...quoteMappingData.vehicle_mappings, newMapping]
    });
  };

  const removeVehicleMapping = (index: number) => {
    const updatedMappings = quoteMappingData.vehicle_mappings.filter((_, i) => i !== index);
    setQuoteMappingData({ ...quoteMappingData, vehicle_mappings: updatedMappings });
  };

  const updateAdditionalCosts = (field: string, value: number) => {
    setQuoteMappingData({
      ...quoteMappingData,
      additional_costs: { ...quoteMappingData.additional_costs, [field]: value }
    });
  };



  const saveQuoteMapping = async () => {
    if (!selectedQuoteId) {
      alert('Please select a quote first');
      return;
    }

    if (!supabase) {
      alert('Database connection not available. Please refresh the page and try again.');
      return;
    }

    setIsSaving(true);
    setSaveStatus('idle');

    try {
      console.log('[QuoteMapping] Starting save operation...');

      // First, ensure table accessibility (this includes auth check)
      try {
        await ensureQuoteMappingsTableExists();
      } catch (accessError) {
        console.error('[QuoteMapping] Table access failed, attempting connection refresh...');

        // Try to refresh connection and retry
        const refreshResult = await refreshConnection();
        if (refreshResult.success) {
          console.log('[QuoteMapping] Connection refreshed, retrying table access...');
          await ensureQuoteMappingsTableExists();
        } else {
          throw accessError; // Re-throw original error if refresh failed
        }
      }

      console.log('[QuoteMapping] Table access verified, proceeding with save...');

      // Prepare the data to save
      const saveData = {
        quote_id: quoteMappingData.quote_id,
        quote_name: quoteMappingData.quote_name,
        customer_name: quoteMappingData.customer_name,
        destination: quoteMappingData.destination,
        hotel_mappings: quoteMappingData.hotel_mappings,
        vehicle_mappings: quoteMappingData.vehicle_mappings,
        additional_costs: quoteMappingData.additional_costs,
        updated_at: new Date().toISOString()
      };

      console.log('[QuoteMapping] Saving data:', saveData);

      // Save to quote_mappings table
      const { data: saveResult, error } = await supabase
        .from('quote_mappings')
        .upsert([saveData], {
          onConflict: 'quote_id'
        });

      console.log('[QuoteMapping] Save result:', { saveResult, error });

      if (error) throw error;

      console.log('[QuoteMapping] Save successful');
      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 3000);

    } catch (error) {
      console.error('[QuoteMapping] Error saving quote mapping:', error);

      // Provide more specific error messages based on error type
      let errorMessage = 'Failed to save quote mapping';

      if (error instanceof Error) {
        const errorMsg = error.message.toLowerCase();

        if (errorMsg.includes('authentication') || errorMsg.includes('auth')) {
          errorMessage = 'Authentication failed. Please refresh the page and log in again.';
        } else if (errorMsg.includes('permission') || errorMsg.includes('42501')) {
          errorMessage = 'Permission denied. Please check your access rights or contact administrator.';
        } else if (errorMsg.includes('quote_mappings table does not exist') || errorMsg.includes('42p01')) {
          errorMessage = 'Database table missing. Please contact administrator to set up the quote_mappings table.';
        } else if (errorMsg.includes('timeout') || errorMsg.includes('timed out')) {
          errorMessage = 'Request timed out. Please check your connection and try again.';
        } else if (errorMsg.includes('404') || errorMsg.includes('not found')) {
          errorMessage = 'Database table not found. Please contact administrator.';
        } else if (errorMsg.includes('network') || errorMsg.includes('connection')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else {
          errorMessage = `Save failed: ${error.message}`;
        }
      }

      alert(errorMessage);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 bg-purple-50">
          <div className="flex items-center gap-3">
            <MapPin className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-800">Quote Mapping for Family Types</h3>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Enhance your baseline quotes with missing cost data to calculate accurate prices for all family types
          </p>
          <div className="mt-3 p-3 bg-purple-100 rounded-md text-sm text-purple-700">
            <strong>Note:</strong> Teenagers (12+) are treated as extra adults. No separate teenager rates needed.
          </div>
        </div>

        <div className="p-6">
          {/* Debug Information */}
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">Debug Information:</h4>
            <div className="text-xs text-yellow-700 space-y-1">
              <p>Supabase Connected: {supabase ? 'Yes' : 'No'}</p>
              <p>Loading Quotes: {isLoadingQuotes ? 'Yes' : 'No'}</p>
              <p>Quotes Loaded: {baselineQuotes.length}</p>
              <p>User Authenticated: {user ? 'Yes' : 'No'}</p>
            </div>
            {!isLoadingQuotes && baselineQuotes.length === 0 && (
              <div className="mt-2 space-x-2">
                <button
                  onClick={fetchBaselineQuotes}
                  className="px-3 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700"
                >
                  Retry Loading Quotes
                </button>
                <button
                  onClick={async () => {
                    const result = await testDatabaseConnection();
                    alert(result.message);
                  }}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                >
                  Test Database
                </button>
                <button
                  onClick={async () => {
                    const result = await checkAuthentication();
                    alert(result.message);
                  }}
                  className="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                >
                  Check Auth
                </button>
                <button
                  onClick={async () => {
                    const result = await getDatabaseInfo();
                    alert(result.message);
                  }}
                  className="px-3 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700"
                >
                  Show Database
                </button>
                <button
                  onClick={async () => {
                    const result = await refreshConnection();
                    alert(result.message);
                    if (result.success) {
                      // Refresh the quotes list after successful connection refresh
                      fetchBaselineQuotes();
                    }
                  }}
                  className="px-3 py-1 bg-orange-600 text-white rounded text-xs hover:bg-orange-700"
                >
                  Refresh Connection
                </button>
              </div>
            )}
          </div>

          {/* Quote Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Baseline Quote to Enhance
            </label>
            <select
              value={selectedQuoteId}
              onChange={(e) => handleQuoteSelection(e.target.value)}
              className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-purple-500 focus:border-purple-500"
              disabled={isLoadingQuotes}
            >
              <option value="">
                {isLoadingQuotes 
                  ? 'Loading quotes...' 
                  : baselineQuotes.length === 0 
                    ? 'No quotes found - try retry button above'
                    : 'Choose a quote to enhance...'
                }
              </option>
              {baselineQuotes.map(quote => (
                <option key={quote.id} value={quote.id}>
                  {quote.customer_name} - {quote.destination} ({quote.family_type}) - {formatPrice(quote.total_cost)} {quote.is_draft ? '(Draft)' : '(Final)'}
                </option>
              ))}
            </select>
            
            {/* Additional help text */}
            {!isLoadingQuotes && baselineQuotes.length === 0 && (
              <div className="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-md">
                <div className="text-sm text-orange-700">
                  <p className="font-medium">No quotes found. This could be because:</p>
                  <ul className="mt-1 ml-4 list-disc space-y-1">
                    <li>You haven't created any quotes yet</li>
                    <li>Database connection issues</li>
                    <li>All quotes have zero or null total_cost</li>
                  </ul>
                  <p className="mt-2 font-medium">Try:</p>
                  <ul className="mt-1 ml-4 list-disc space-y-1">
                    <li>Create a quote in the Quote Generator tab first</li>
                    <li>Check your internet connection</li>
                    <li>Use the retry button above</li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* Selected Quote Info */}
          {selectedQuote && (
            <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-md">
              <h4 className="font-medium text-purple-800 mb-2">Selected Quote Details:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm text-purple-700">
                <div>
                  <p><strong>Package:</strong> {selectedQuote.package_name}</p>
                  <p><strong>Customer:</strong> {selectedQuote.customer_name}</p>
                  <p><strong>Destination:</strong> {selectedQuote.destination}</p>
                </div>
                <div>
                  <p><strong>Family Type:</strong> {selectedQuote.family_type}</p>
                  <p><strong>Total Cost:</strong> {formatPrice(selectedQuote.total_cost)}</p>
                  <p><strong>Composition:</strong> {selectedQuote.no_of_persons + selectedQuote.extra_adults} Adults, {selectedQuote.children} Children, {selectedQuote.infants} Infants</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {selectedQuote && (
        <>
          {/* Hotel Cost Mappings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h4 className="text-lg font-semibold text-gray-800">Hotel Cost Mappings</h4>
              <p className="text-sm text-gray-600">Add missing cost data for different age groups</p>
            </div>
            <div className="p-6">
              {quoteMappingData.hotel_mappings.map((hotel, index) => (
                <div key={index} className="mb-6 p-4 border border-gray-200 rounded-md">
                  <h5 className="font-medium text-gray-800 mb-3">{hotel.hotel_name}</h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Extra Adult Cost</label>
                      <input
                        type="number"
                        value={hotel.extra_adult_cost}
                        onChange={(e) => updateHotelMapping(index, 'extra_adult_cost', Number(e.target.value))}
                        className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                        placeholder="Per night"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Children Cost (6-12)</label>
                      <input
                        type="number"
                        value={hotel.children_cost}
                        onChange={(e) => updateHotelMapping(index, 'children_cost', Number(e.target.value))}
                        className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                        placeholder="Per night"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Infant Cost</label>
                      <input
                        type="number"
                        value={hotel.infant_cost}
                        onChange={(e) => updateHotelMapping(index, 'infant_cost', Number(e.target.value))}
                        className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                        placeholder="Usually 0"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Grandparent Discount (%)</label>
                      <input
                        type="number"
                        value={hotel.grandparent_discount}
                        onChange={(e) => updateHotelMapping(index, 'grandparent_discount', Number(e.target.value))}
                        className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                        placeholder="10"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Vehicle Cost Mappings */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-gray-800">Vehicle Cost Mappings</h4>
                  <p className="text-sm text-gray-600">Set costs using multipliers or actual amounts for different vehicle types</p>
                </div>
                <button
                  onClick={addVehicleMapping}
                  className="px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center gap-1 text-sm"
                >
                  <Plus className="h-4 w-4" />
                  Add Vehicle
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {quoteMappingData.vehicle_mappings.map((vehicle, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-md">
                    <div className="grid grid-cols-6 gap-4 items-end">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Vehicle Type</label>
                        <select
                          value={vehicle.vehicle_type}
                          onChange={(e) => updateVehicleMapping(index, 'vehicle_type', e.target.value)}
                          className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                        >
                          {VEHICLE_TYPE_OPTIONS.map(type => (
                            <option key={type} value={type}>{type}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Pricing Type</label>
                        <select
                          value={vehicle.pricing_type}
                          onChange={(e) => updateVehicleMapping(index, 'pricing_type', e.target.value)}
                          className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                        >
                          <option value="multiplier">Multiplier</option>
                          <option value="actual_cost">Actual Cost</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          {vehicle.pricing_type === 'actual_cost' ? 'Actual Cost (₹)' : 'Base Cost (₹)'}
                        </label>
                        <input
                          type="number"
                          value={vehicle.base_cost}
                          onChange={(e) => updateVehicleMapping(index, 'base_cost', Number(e.target.value))}
                          className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                          placeholder={vehicle.pricing_type === 'actual_cost' ? 'Fixed price' : 'Base amount'}
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Cost Multiplier {vehicle.pricing_type === 'actual_cost' ? '(unused)' : ''}
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={vehicle.cost_multiplier}
                          onChange={(e) => updateVehicleMapping(index, 'cost_multiplier', Number(e.target.value))}
                          className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                          placeholder="1.0"
                          disabled={vehicle.pricing_type === 'actual_cost'}
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Max Capacity</label>
                        <input
                          type="number"
                          value={vehicle.max_capacity}
                          onChange={(e) => updateVehicleMapping(index, 'max_capacity', Number(e.target.value))}
                          className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                          placeholder="4"
                        />
                      </div>
                      <div className="flex items-center gap-2">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={vehicle.is_active}
                            onChange={(e) => updateVehicleMapping(index, 'is_active', e.target.checked)}
                            className="mr-1"
                          />
                          <span className="text-xs text-gray-700">Active</span>
                        </label>
                        <button
                          onClick={() => removeVehicleMapping(index)}
                          className="p-1 text-red-600 hover:text-red-800"
                          title="Remove vehicle"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Additional Costs */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h4 className="text-lg font-semibold text-gray-800">Additional Cost Mappings</h4>
              <p className="text-sm text-gray-600">Set per-person or per-day costs for various services</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {Object.entries(quoteMappingData.additional_costs).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      {key === 'meal_cost_per_person' ? 'Meal Cost (per person)' :
                       key === 'ferry_cost' ? 'Ferry Cost' :
                       key === 'activity_cost_per_person' ? 'Activity Cost (per person)' :
                       key === 'guide_cost_per_day' ? 'Guide Cost (per day)' :
                       key === 'parking_toll_multiplier' ? 'Parking/Toll Multiplier' :
                       key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    <input
                      type="number"
                      step={key === 'parking_toll_multiplier' ? '0.1' : '1'}
                      value={value}
                      onChange={(e) => updateAdditionalCosts(key, Number(e.target.value))}
                      className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={saveQuoteMapping}
              disabled={!selectedQuoteId || isSaving}
              className={`px-6 py-2 text-sm font-medium rounded-md transition-colors ${
                !selectedQuoteId || isSaving
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : saveStatus === 'success'
                  ? 'bg-green-600 text-white'
                  : saveStatus === 'error'
                  ? 'bg-red-600 text-white'
                  : 'bg-purple-600 text-white hover:bg-purple-700'
              }`}
            >
              {isSaving ? 'Saving...' : saveStatus === 'success' ? 'Saved!' : saveStatus === 'error' ? 'Error - Retry' : 'Save Mapping'}
            </button>
          </div>
        </>
      )}
    </div>
  );
}

export default QuoteMappingTab; 